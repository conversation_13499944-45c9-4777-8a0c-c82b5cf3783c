import 'package:builder_app/core/utils/app_colors.dart';
import 'package:builder_app/features/login/presentation/widgets/common_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../bloc/auth/login_bloc.dart';
import '../bloc/auth/login_event.dart';
import '../bloc/auth/login_state.dart';
import '../../data/datasources/login_remote_data_source.dart';
import '../../data/repositories/login_repository_impl.dart';
import '../../domain/usecases/login_user.dart';
import 'package:builder_app/core/utils/global_snackbar.dart';
import 'package:responsive_framework/responsive_framework.dart';
import 'package:builder_app/features/login/presentation/bloc/app_config/config_bloc.dart';

import '../widgets/common_text_field.dart';

class LoginPage extends StatelessWidget {
  final String? redirectMessage;
  final String? email;
  
  const LoginPage({
    super.key,
    this.redirectMessage,
    this.email,
  });

  @override
  Widget build(BuildContext context) {
    final remoteDataSource = LoginRemoteDataSourceImpl();
    final repository = LoginRepositoryImpl(remoteDataSource: remoteDataSource);
    final loginUser = LoginUser(repository);

    return BlocProvider(
      create: (_) => LoginBloc(loginUser: loginUser),
      child: LoginView(
        redirectMessage: redirectMessage,
        email: email,
      ),
    );
  }
}

class LoginView extends StatefulWidget {
  final String? redirectMessage;
  final String? email;
  
  const LoginView({
    super.key,
    this.redirectMessage,
    this.email,
  });

  @override
  State<LoginView> createState() => _LoginViewState();
}

class _LoginViewState extends State<LoginView> {
  final TextEditingController _emailController = TextEditingController(text: 'testuser12');
  final TextEditingController _passwordController = TextEditingController(text: 'test@123');
  final GlobalKey<FormState> _formKey=GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    // Pre-fill email if provided
    if (widget.email != null) {
      _emailController.text = widget.email!;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      
      body: _buildLoginContent(context),
    );
  }

  Widget _buildLoginContent(BuildContext context) {
    return Center(
      child: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Show redirect message if provided
              if (widget.redirectMessage != null)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    widget.redirectMessage!,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                  ),
                ),
              BlocConsumer<LoginBloc, LoginState>(
                listener: (context, state) {
                  if (state is LoginFailure) {
                    GlobalSnackbar.show(
                      state.message,
                      action: SnackBarAction(label: "Hide", onPressed: GlobalSnackbar.hide),
                      backgroundColor: AppColors.primary,
                    );
                  } else if (state is LoginSuccess) {
                    GlobalSnackbar.show(
                      'Login Successful! ${state.message}',
                      action: SnackBarAction(label: "Hide", onPressed: GlobalSnackbar.hide),
                      backgroundColor: AppColors.primary,
                    );
                    // Trigger AppConfigBloc to update login state so go_router can redirect
                    context.read<AppConfigBloc>().checkLoginStatus();
                  }
                },
                builder: (context, state) {
                  // Responsive layout
                  if (ResponsiveBreakpoints.of(context).isMobile) {
                    return _buildMobileLayout(context, state);
                  } else if (ResponsiveBreakpoints.of(context).isTablet) {
                    return _buildTabletLayout(context, state);
                  } else {
                    return _buildDesktopLayout(context, state);
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMobileLayout(BuildContext context, LoginState state) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final iconColor = isDark ? AppColors.surfaceLight : Theme.of(context).colorScheme.primary;
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        const SizedBox(height: 20),
        Center(
          child: Icon(
            Icons.person,
            size: 60,
            color: iconColor,
          ),
        ),
        const SizedBox(height: 20),
        _buildLoginForm(context, state),
      ],
    );
  }

  Widget _buildTabletLayout(BuildContext context, LoginState state) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final iconColor = isDark ? AppColors.surfaceLight : Theme.of(context).colorScheme.primary;
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: Center(
            child: Icon(
              Icons.person,
              size: 80,
              color: iconColor,
            ),
          ),
        ),
        const SizedBox(width: 40),
        Expanded(
          flex: 2,
          child: _buildLoginForm(context, state),
        ),
      ],
    );
  }

  Widget _buildDesktopLayout(BuildContext context, LoginState state) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final iconColor = isDark ? AppColors.surfaceLight : Theme.of(context).colorScheme.primary;
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: Center(
            child: Icon(
              Icons.person,
              size: 250,
              color: iconColor,
            ),
          ),
        ),
        const SizedBox(width: 60),
        Expanded(
          flex: 2,
          child: _buildLoginForm(context, state),
        ),
      ],
    );
  }

  Widget _buildLoginForm(BuildContext context, LoginState state) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final buttonBgColor = isDark ? AppColors.surfaceLight : Theme.of(context).colorScheme.secondary;
    final buttonTextColor = isDark ? Colors.black : Theme.of(context).colorScheme.onPrimary;
    final spinnerColor = isDark ? Colors.black87 : Theme.of(context).colorScheme.onPrimary;
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        CommonTextField(
          controller: _emailController,
          label: 'User Id',
          autovalidateMode: AutovalidateMode.onUserInteraction,
          decoration: const InputDecoration(
            hintText: 'User Id',
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) return 'Enter ID';
            // if (!RegExp(r'^[^@\s]+@[^@\s]+\.[^@\s]+').hasMatch(value)) return 'Enter a valid email';
            return null;
          },
        ),
        const SizedBox(height: 16),
        CommonTextField(
          controller: _passwordController,
          label: 'Password',
          obscureText: true,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          decoration: const InputDecoration(
            hintText: 'Password',
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) return 'Enter password';
            if (value.length < 6) return 'Password must be at least 6 characters';
            return null;
          },
        ),
        const SizedBox(height: 24),
         CommonButton(
          tooltip: 'Login',
          text: 'Login',
          textStyle: Theme.of(context).textTheme.labelLarge?.copyWith(
            color: buttonTextColor,
          ),
          backgroundColor: buttonBgColor,
          onPressed: () {
            context.go('/home');
          },
          // state is LoginLoading ? (){} : () {
          //   if (_formKey.currentState?.validate() ?? false) {
          //     context.read<LoginBloc>().add(
          //       UserLoginEvent(
          //         email: _emailController.text,
          //         password: _passwordController.text,
          //       ),
          //     );
          //   }
          // },
          child: state is LoginLoading
              ? CircularProgressIndicator(strokeWidth: 2, color: spinnerColor)
              : null,
        ),
      ],
    );
  }
}
