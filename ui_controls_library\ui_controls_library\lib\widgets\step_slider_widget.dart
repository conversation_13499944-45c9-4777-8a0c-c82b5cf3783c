import 'dart:convert';
import 'package:flutter/material.dart';

/// A customizable step slider widget.
///
/// This widget provides a slider with discrete steps and various customization options
/// including min/max values, step size, colors, labels, and more.
class StepSliderWidget extends StatefulWidget {
  /// Minimum value for the slider
  final double min;

  /// Maximum value for the slider
  final double max;

  /// Current value of the slider
  final double initial;

  /// Number of discrete steps for the slider
  final int steps;

  /// Color of the active portion of the slider
  final Color activeColor;

  /// Color of the inactive portion of the slider
  final Color? inactiveColor;

  /// Color of the slider thumb
  final Color? thumbColor;

  /// Height of the slider track
  final double? trackHeight;

  /// Style for the slider label
  final TextStyle? labelStyle;

  /// Whether to show the value label
  final bool showLabel;

  /// Whether to show step markers
  final bool showStepMarkers;

  /// Whether to show step labels
  final bool showStepLabels;

  /// Custom label format function
  final String Function(double)? labelFormat;

  /// Whether to snap to steps
  final bool snapToSteps;

  /// Whether the slider is vertical
  final bool isVertical;

  /// Height/length of the slider
  final double? height;

  /// Width of the slider
  final double? width;

  /// Padding around the slider
  final EdgeInsetsGeometry padding;

  /// Margin around the slider
  final EdgeInsetsGeometry margin;

  /// Whether to show min/max labels
  final bool showMinMaxLabels;

  /// Style for min/max labels
  final TextStyle? minMaxLabelStyle;

  /// Whether to show value display
  final bool showValueDisplay;

  /// Style for value display
  final TextStyle? valueDisplayStyle;

  /// Prefix for the value display
  final String? prefix;

  /// Suffix for the value display
  final String? suffix;

  /// Number of decimal places to show in the value display
  final int decimalPlaces;

  /// Whether the slider is disabled
  final bool isDisabled;

  /// Callback when the slider value changes
  final ValueChanged<double>? onChanged;

  /// Callback when the slider value change ends
  final ValueChanged<double>? onChangeEnd;

  /// JSON configuration for the widget
  final Map<String, dynamic>? jsonConfig;

  /// Whether to use JSON configuration
  final bool useJsonConfig;

  /// JSON callbacks
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use JSON callbacks
  final bool useJsonCallbacks;

  const StepSliderWidget({
    super.key,
    this.min = 0.0,
    this.max = 100.0,
    this.initial = 0.0,
    this.steps = 5,
    this.activeColor = const Color(0xFF0058FF),
    this.inactiveColor,
    this.thumbColor,
    this.trackHeight,
    this.labelStyle,
    this.showLabel = true,
    this.showStepMarkers = false,
    this.showStepLabels = false,
    this.labelFormat,
    this.snapToSteps = true,
    this.isVertical = false,
    this.height,
    this.width,
    this.padding = const EdgeInsets.symmetric(horizontal: 0.0, vertical: 8.0),
    this.margin = EdgeInsets.zero,
    this.showMinMaxLabels = false,
    this.minMaxLabelStyle,
    this.showValueDisplay = true,
    this.valueDisplayStyle,
    this.prefix,
    this.suffix,
    this.decimalPlaces = 0,
    this.isDisabled = false,
    this.onChanged,
    this.onChangeEnd,
    this.jsonConfig,
    this.useJsonConfig = false,
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
  });

  /// Helper method to parse a color from a string or map
  static Color _parseColor(
    dynamic colorValue, [
    Color defaultColor = Colors.blue,
  ]) {
    if (colorValue == null) return defaultColor;

    if (colorValue is String) {
      if (colorValue.startsWith('#')) {
        // Parse hex color
        String hex = colorValue.replaceFirst('#', '');
        if (hex.length == 3) {
          // Convert 3-digit hex to 6-digit
          hex = hex.split('').map((char) => char + char).join('');
        }
        if (hex.length == 6) {
          hex = 'FF$hex'; // Add alpha channel if not present
        }
        return Color(int.parse(hex, radix: 16));
      } else {
        // Parse named color
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Colors.blue;
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          case 'transparent':
            return Colors.transparent;
          default:
            return defaultColor;
        }
      }
    } else if (colorValue is Map) {
      // Parse RGBA color
      final int r = colorValue['r'] as int? ?? 0;
      final int g = colorValue['g'] as int? ?? 0;
      final int b = colorValue['b'] as int? ?? 0;
      final double a =
          colorValue['a'] != null ? (colorValue['a'] as num).toDouble() : 1.0;
      return Color.fromRGBO(r, g, b, a);
    }
    return defaultColor;
  }

  /// Helper method to convert a color to a string
  static String _colorToString(Color color) {
    final r = color.toString().split('(0x')[1].substring(2, 4);
    final g = color.toString().split('(0x')[1].substring(4, 6);
    final b = color.toString().split('(0x')[1].substring(6, 8);
    return '#$r$g$b';
  }

  /// Helper method to parse EdgeInsets from a JSON object
  static EdgeInsetsGeometry _parseEdgeInsets(dynamic value) {
    if (value is String) {
      // Parse from string like "all:8.0" or "symmetric:horizontal:8.0,vertical:16.0"
      if (value.startsWith('all:')) {
        final padding = double.tryParse(value.split(':')[1]) ?? 8.0;
        return EdgeInsets.all(padding);
      } else if (value.startsWith('symmetric:')) {
        final parts = value.split(':');
        if (parts.length >= 4) {
          final horizontal = double.tryParse(parts[2].split(',')[0]) ?? 8.0;
          final vertical = double.tryParse(parts[3]) ?? 8.0;
          return EdgeInsets.symmetric(
            horizontal: horizontal,
            vertical: vertical,
          );
        }
      }
      return const EdgeInsets.all(8.0);
    } else if (value is Map) {
      // Parse from map
      if (value.containsKey('all')) {
        return EdgeInsets.all((value['all'] as num).toDouble());
      } else if (value.containsKey('symmetric')) {
        final symmetric = value['symmetric'] as Map;
        return EdgeInsets.symmetric(
          horizontal:
              symmetric['horizontal'] != null
                  ? (symmetric['horizontal'] as num).toDouble()
                  : 0.0,
          vertical:
              symmetric['vertical'] != null
                  ? (symmetric['vertical'] as num).toDouble()
                  : 0.0,
        );
      } else {
        return EdgeInsets.fromLTRB(
          value['left'] != null ? (value['left'] as num).toDouble() : 0.0,
          value['top'] != null ? (value['top'] as num).toDouble() : 0.0,
          value['right'] != null ? (value['right'] as num).toDouble() : 0.0,
          value['bottom'] != null ? (value['bottom'] as num).toDouble() : 0.0,
        );
      }
    } else if (value is num) {
      return EdgeInsets.all(value.toDouble());
    }
    return const EdgeInsets.all(8.0);
  }

  /// Helper method to convert EdgeInsets to a JSON object
  static Map<String, dynamic> _edgeInsetsToJson(EdgeInsetsGeometry edgeInsets) {
    if (edgeInsets is EdgeInsets) {
      if (edgeInsets.left == edgeInsets.top &&
          edgeInsets.left == edgeInsets.right &&
          edgeInsets.left == edgeInsets.bottom) {
        return {'all': edgeInsets.left};
      } else if (edgeInsets.left == edgeInsets.right &&
          edgeInsets.top == edgeInsets.bottom) {
        return {
          'symmetric': {
            'horizontal': edgeInsets.left,
            'vertical': edgeInsets.top,
          },
        };
      } else {
        return {
          'left': edgeInsets.left,
          'top': edgeInsets.top,
          'right': edgeInsets.right,
          'bottom': edgeInsets.bottom,
        };
      }
    }
    return {'all': 8.0};
  }

  /// Helper method to parse a TextStyle from a JSON object
  static TextStyle? _parseTextStyle(dynamic value) {
    if (value == null) return null;

    if (value is Map) {
      Color? color;
      if (value['color'] != null) {
        color = _parseColor(value['color']);
      }

      double? fontSize;
      if (value['fontSize'] != null) {
        fontSize = (value['fontSize'] as num).toDouble();
      }

      FontWeight? fontWeight;
      if (value['fontWeight'] != null) {
        final weight = value['fontWeight'];
        if (weight is String) {
          switch (weight.toLowerCase()) {
            case 'bold':
              fontWeight = FontWeight.bold;
              break;
            case 'normal':
              fontWeight = FontWeight.normal;
              break;
            case 'light':
              fontWeight = FontWeight.w300;
              break;
            case 'thin':
              fontWeight = FontWeight.w100;
              break;
            case 'medium':
              fontWeight = FontWeight.w500;
              break;
            case 'semibold':
              fontWeight = FontWeight.w600;
              break;
            case 'black':
              fontWeight = FontWeight.w900;
              break;
          }
        } else if (weight is int) {
          switch (weight) {
            case 100:
              fontWeight = FontWeight.w100;
              break;
            case 200:
              fontWeight = FontWeight.w200;
              break;
            case 300:
              fontWeight = FontWeight.w300;
              break;
            case 400:
              fontWeight = FontWeight.w400;
              break;
            case 500:
              fontWeight = FontWeight.w500;
              break;
            case 600:
              fontWeight = FontWeight.w600;
              break;
            case 700:
              fontWeight = FontWeight.w700;
              break;
            case 800:
              fontWeight = FontWeight.w800;
              break;
            case 900:
              fontWeight = FontWeight.w900;
              break;
          }
        }
      }

      FontStyle? fontStyle;
      if (value['fontStyle'] != null) {
        final style = value['fontStyle'].toString().toLowerCase();
        if (style == 'italic') {
          fontStyle = FontStyle.italic;
        } else if (style == 'normal') {
          fontStyle = FontStyle.normal;
        }
      }

      return TextStyle(
        color: color,
        fontSize: fontSize,
        fontWeight: fontWeight,
        fontStyle: fontStyle,
      );
    }

    return null;
  }

  /// Helper method to convert a TextStyle to a JSON object
  static Map<String, dynamic>? _textStyleToJson(TextStyle style) {
    final Map<String, dynamic> result = {};

    if (style.color != null) {
      result['color'] = _colorToString(style.color!);
    }

    if (style.fontSize != null) {
      result['fontSize'] = style.fontSize;
    }

    if (style.fontWeight != null) {
      if (style.fontWeight == FontWeight.bold) {
        result['fontWeight'] = 'bold';
      } else if (style.fontWeight == FontWeight.normal) {
        result['fontWeight'] = 'normal';
      } else if (style.fontWeight == FontWeight.w100) {
        result['fontWeight'] = 'thin';
      } else if (style.fontWeight == FontWeight.w300) {
        result['fontWeight'] = 'light';
      } else if (style.fontWeight == FontWeight.w500) {
        result['fontWeight'] = 'medium';
      } else if (style.fontWeight == FontWeight.w600) {
        result['fontWeight'] = 'semibold';
      } else if (style.fontWeight == FontWeight.w900) {
        result['fontWeight'] = 'black';
      } else {
        result['fontWeight'] = (style.fontWeight!.index + 1) * 100;
      }
    }

    if (style.fontStyle != null) {
      result['fontStyle'] =
          style.fontStyle == FontStyle.italic ? 'italic' : 'normal';
    }

    return result.isNotEmpty ? result : null;
  }

  /// Creates a StepSliderWidget from a JSON map
  ///
  /// This factory constructor allows for creating a fully configured step slider widget
  /// from a JSON object, making it easy to load configurations from external sources.
  factory StepSliderWidget.fromJson(dynamic jsonData) {
    // Handle string JSON input
    Map<String, dynamic> json;
    if (jsonData is String) {
      json = jsonDecode(jsonData) as Map<String, dynamic>;
    } else if (jsonData is Map<String, dynamic>) {
      json = jsonData;
    } else {
      throw ArgumentError('Invalid JSON data: $jsonData');
    }

    // Parse basic properties
    final min = json['min'] != null ? (json['min'] as num).toDouble() : 0.0;
    final max = json['max'] != null ? (json['max'] as num).toDouble() : 100.0;
    final initial =
        json['initial'] != null ? (json['initial'] as num).toDouble() : min;
    final steps = json['steps'] as int? ?? 5;

    // Parse colors
    final activeColor =
        json['activeColor'] != null
            ? _parseColor(json['activeColor'])
            : const Color(0xFF0058FF);
    final inactiveColor =
        json['inactiveColor'] != null
            ? _parseColor(json['inactiveColor'])
            : null;
    final thumbColor =
        json['thumbColor'] != null ? _parseColor(json['thumbColor']) : null;

    // Parse appearance properties
    final trackHeight =
        json['trackHeight'] != null
            ? (json['trackHeight'] as num).toDouble()
            : null;
    final labelStyle =
        json['labelStyle'] != null ? _parseTextStyle(json['labelStyle']) : null;
    final showLabel = json['showLabel'] as bool? ?? true;
    final showStepMarkers = json['showStepMarkers'] as bool? ?? true;
    final showStepLabels = json['showStepLabels'] as bool? ?? false;

    // Parse behavior properties
    final snapToSteps = json['snapToSteps'] as bool? ?? true;
    final isVertical = json['isVertical'] as bool? ?? false;

    // Parse size properties
    final height =
        json['height'] != null ? (json['height'] as num).toDouble() : null;
    final width =
        json['width'] != null ? (json['width'] as num).toDouble() : null;

    // Parse padding and margin
    final padding =
        json['padding'] != null
            ? _parseEdgeInsets(json['padding'])
            : const EdgeInsets.symmetric(horizontal: 0.0, vertical: 8.0);
    final margin =
        json['margin'] != null
            ? _parseEdgeInsets(json['margin'])
            : EdgeInsets.zero;

    // Parse display properties
    final showMinMaxLabels = json['showMinMaxLabels'] as bool? ?? false;
    final minMaxLabelStyle =
        json['minMaxLabelStyle'] != null
            ? _parseTextStyle(json['minMaxLabelStyle'])
            : null;
    final showValueDisplay = json['showValueDisplay'] as bool? ?? true;
    final valueDisplayStyle =
        json['valueDisplayStyle'] != null
            ? _parseTextStyle(json['valueDisplayStyle'])
            : null;

    // Parse formatting properties
    final prefix = json['prefix'] as String?;
    final suffix = json['suffix'] as String?;
    final decimalPlaces = json['decimalPlaces'] as int? ?? 0;

    // Parse state properties
    final isDisabled = json['isDisabled'] as bool? ?? false;

    // Parse JSON configuration
    final jsonConfig = json['jsonConfig'] as Map<String, dynamic>?;
    final useJsonConfig = json['useJsonConfig'] as bool? ?? false;
    final jsonCallbacks = json['jsonCallbacks'] as Map<String, dynamic>?;
    final useJsonCallbacks = json['useJsonCallbacks'] as bool? ?? false;

    // Create and return the widget
    return StepSliderWidget(
      min: min,
      max: max,
      initial: initial,
      steps: steps,
      activeColor: activeColor,
      inactiveColor: inactiveColor,
      thumbColor: thumbColor,
      trackHeight: trackHeight,
      labelStyle: labelStyle,
      showLabel: showLabel,
      showStepMarkers: showStepMarkers,
      showStepLabels: showStepLabels,
      snapToSteps: snapToSteps,
      isVertical: isVertical,
      height: height,
      width: width,
      padding: padding,
      margin: margin,
      showMinMaxLabels: showMinMaxLabels,
      minMaxLabelStyle: minMaxLabelStyle,
      showValueDisplay: showValueDisplay,
      valueDisplayStyle: valueDisplayStyle,
      prefix: prefix,
      suffix: suffix,
      decimalPlaces: decimalPlaces,
      isDisabled: isDisabled,
      jsonConfig: jsonConfig,
      useJsonConfig: useJsonConfig,
      jsonCallbacks: jsonCallbacks,
      useJsonCallbacks: useJsonCallbacks,
    );
  }

  /// Converts the StepSliderWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    return {
      'min': min,
      'max': max,
      'initial': initial,
      'steps': steps,
      'activeColor': _colorToString(activeColor),
      'inactiveColor':
          inactiveColor != null ? _colorToString(inactiveColor!) : null,
      'thumbColor': thumbColor != null ? _colorToString(thumbColor!) : null,
      'trackHeight': trackHeight,
      'labelStyle': labelStyle != null ? _textStyleToJson(labelStyle!) : null,
      'showLabel': showLabel,
      'showStepMarkers': showStepMarkers,
      'showStepLabels': showStepLabels,
      'snapToSteps': snapToSteps,
      'isVertical': isVertical,
      'height': height,
      'width': width,
      'padding': _edgeInsetsToJson(padding),
      'margin': _edgeInsetsToJson(margin),
      'showMinMaxLabels': showMinMaxLabels,
      'minMaxLabelStyle':
          minMaxLabelStyle != null ? _textStyleToJson(minMaxLabelStyle!) : null,
      'showValueDisplay': showValueDisplay,
      'valueDisplayStyle':
          valueDisplayStyle != null
              ? _textStyleToJson(valueDisplayStyle!)
              : null,
      'prefix': prefix,
      'suffix': suffix,
      'decimalPlaces': decimalPlaces,
      'isDisabled': isDisabled,
      'useJsonConfig': useJsonConfig,
      'useJsonCallbacks': useJsonCallbacks,
    };
  }

  @override
  State<StepSliderWidget> createState() => _StepSliderWidgetState();
}

class _StepSliderWidgetState extends State<StepSliderWidget> {
  late double _currentValue;

  @override
  void initState() {
    super.initState();
    _currentValue = widget.initial.clamp(widget.min, widget.max);

    // If snapToSteps is true, snap the initial value to the nearest step
    if (widget.snapToSteps) {
      _currentValue = _snapToStep(_currentValue);
    }
  }

  @override
  void didUpdateWidget(StepSliderWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update current value if the widget's initial value changes
    if (oldWidget.initial != widget.initial) {
      _currentValue = widget.initial.clamp(widget.min, widget.max);

      // If snapToSteps is true, snap the initial value to the nearest step
      if (widget.snapToSteps) {
        _currentValue = _snapToStep(_currentValue);
      }
    }
  }

  /// Snaps a value to the nearest step
  double _snapToStep(double value, [StepSliderWidget? config]) {
    final cfg = config ?? widget;
    if (cfg.steps <= 1) return value;

    final stepSize = (cfg.max - cfg.min) / cfg.steps;
    final steps = ((value - cfg.min) / stepSize).round();
    return (cfg.min + steps * stepSize).clamp(cfg.min, cfg.max);
  }

  /// Formats a value for display
  String _formatValue(double value, [StepSliderWidget? config]) {
    final cfg = config ?? widget;
    if (cfg.labelFormat != null) {
      return cfg.labelFormat!(value);
    }

    final formattedValue = value.toStringAsFixed(cfg.decimalPlaces);
    final prefix = cfg.prefix ?? '';
    final suffix = cfg.suffix ?? '';

    return '$prefix$formattedValue$suffix';
  }

  @override
  Widget build(BuildContext context) {
    // Apply JSON configuration if available
    if (widget.useJsonConfig && widget.jsonConfig != null) {
      // Create a new widget with the JSON configuration applied
      final jsonWidget = StepSliderWidget.fromJson(widget.jsonConfig!);

      // Use the JSON widget's properties for rendering, but keep the current state
      return _buildWidgetWithConfig(context, jsonWidget);
    }

    return _buildWidgetWithConfig(context, widget);
  }

  /// Builds the widget with the given configuration
  Widget _buildWidgetWithConfig(BuildContext context, StepSliderWidget config) {
    // Calculate divisions based on steps
    // Always use divisions when steps are specified
    final divisions = config.steps > 1 ? config.steps : null;

    // Create slider theme data
    final sliderThemeData = SliderThemeData(
      trackHeight: config.trackHeight,
      //activeTrackColor: config.activeColor,
      activeTrackColor: Color(0xFF0058FF),
      inactiveTrackColor: config.inactiveColor ?? Colors.grey.shade300,
      thumbColor: Colors.transparent,
      overlayColor: Colors.transparent,
      trackShape: const CustomSliderTrackShape(),
      thumbShape: StepSliderThumbCircle(
        thumbRadius: 12,
        thumbColor: Color(0xFF0058FF),
        min: config.min,
        max: config.max,
        textStyle: const TextStyle(
          fontSize: 12,
          fontFamily: 'Inter',
          color: Colors.white,
          fontWeight: FontWeight.normal,
        ),
      ),
      overlayShape: SliderComponentShape.noOverlay,
      showValueIndicator: ShowValueIndicator.never,
      tickMarkShape:
          config.showStepMarkers
              ? const RoundSliderTickMarkShape(tickMarkRadius: 3.0)
              : SliderTickMarkShape.noTickMark,
    );

    // Create the slider
    Widget slider = SliderTheme(
      data: sliderThemeData,
      child: Slider(
        value: _currentValue,
        min: config.min,
        max: config.max,
        divisions: divisions,
        label: config.showLabel ? _formatValue(_currentValue, config) : null,
        //activeColor: config.activeColor,
        activeColor: Color(0xFF0058FF),
        inactiveColor: config.inactiveColor,
        onChanged:
            config.isDisabled
                ? null
                : (value) {
                  setState(() {
                    _currentValue = value;
                  });
                  if (widget.onChanged != null) {
                    widget.onChanged!(value);
                  }
                },
        onChangeEnd:
            config.isDisabled
                ? null
                : (value) {
                  // If snapToSteps is true and we're not using divisions, snap the value
                  if (config.snapToSteps && divisions == null) {
                    final snappedValue = _snapToStep(value, config);
                    setState(() {
                      _currentValue = snappedValue;
                    });
                    if (widget.onChangeEnd != null) {
                      widget.onChangeEnd!(snappedValue);
                    }
                  } else if (widget.onChangeEnd != null) {
                    widget.onChangeEnd!(value);
                  }
                },
      ),
    );

    // If vertical, rotate the slider
    if (config.isVertical) {
      slider = RotatedBox(
        quarterTurns: 3, // Rotate to make horizontal slider vertical
        child: slider,
      );
    }

    // Create the main content
    List<Widget> children = [];

    // Add label if needed
    // if (config.showValueDisplay) {
    //   children.add(
    //     Padding(
    //       padding: const EdgeInsets.only(bottom: 8.0),
    //       child: Text(
    //         _formatValue(_currentValue, config),
    //         style: config.valueDisplayStyle ?? const TextStyle(fontSize: 16.0, fontWeight: FontWeight.bold),
    //         textAlign: TextAlign.center,
    //       ),
    //     ),
    //   );
    // }

    // Add min/max labels if needed
    if (config.showMinMaxLabels) {
      children.add(
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              _formatValue(config.min, config),
              style: config.minMaxLabelStyle ?? const TextStyle(fontSize: 12.0),
            ),
            Text(
              _formatValue(config.max, config),
              style: config.minMaxLabelStyle ?? const TextStyle(fontSize: 12.0),
            ),
          ],
        ),
      );
    }

    // Add slider
    children.add(slider);

    // Add step labels if needed
    if (config.showStepLabels && config.steps > 1) {
      final stepSize = (config.max - config.min) / config.steps;
      final stepLabels = List<Widget>.generate(config.steps + 1, (index) {
        final value = config.min + index * stepSize;
        return Text(
          _formatValue(value, config),
          style: const TextStyle(fontSize: 10.0),
        );
      });

      children.add(
        Padding(
          padding: const EdgeInsets.only(top: 4.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: stepLabels,
          ),
        ),
      );
    }

    // Create the final widget
    return Container(
      width: config.width,
      height: config.height,
      //padding: config.padding,
      //margin: config.margin,
      child: Column(mainAxisSize: MainAxisSize.min, children: children),
    );
  }
}

/// Custom slider track shape with no padding
class CustomSliderTrackShape extends SliderTrackShape
    with BaseSliderTrackShape {
  const CustomSliderTrackShape();

  @override
  Rect getPreferredRect({
    required RenderBox parentBox,
    Offset offset = Offset.zero,
    required SliderThemeData sliderTheme,
    bool isEnabled = false,
    bool isDiscrete = false,
  }) {
    final double trackHeight = sliderTheme.trackHeight ?? 4.0;
    final double trackLeft = offset.dx;
    final double trackTop =
        offset.dy + (parentBox.size.height - trackHeight) / 2;
    final double trackWidth = parentBox.size.width;
    return Rect.fromLTWH(trackLeft, trackTop, trackWidth, trackHeight);
  }

  @override
  void paint(
    PaintingContext context,
    Offset offset, {
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required Animation<double> enableAnimation,
    required TextDirection textDirection,
    required Offset thumbCenter,
    Offset? secondaryOffset,
    bool isDiscrete = false,
    bool isEnabled = false,
    double additionalActiveTrackHeight = 2,
  }) {
    assert(sliderTheme.disabledActiveTrackColor != null);
    assert(sliderTheme.disabledInactiveTrackColor != null);
    assert(sliderTheme.activeTrackColor != null);
    assert(sliderTheme.inactiveTrackColor != null);
    assert(sliderTheme.thumbShape != null);

    if (sliderTheme.trackHeight == null || sliderTheme.trackHeight! <= 0) {
      return;
    }

    final Rect trackRect = getPreferredRect(
      parentBox: parentBox,
      offset: offset,
      sliderTheme: sliderTheme,
      isEnabled: isEnabled,
      isDiscrete: isDiscrete,
    );

    final ColorTween activeTrackColorTween = ColorTween(
      begin: sliderTheme.disabledActiveTrackColor,
      end: sliderTheme.activeTrackColor,
    );
    final ColorTween inactiveTrackColorTween = ColorTween(
      begin: sliderTheme.disabledInactiveTrackColor,
      end: sliderTheme.inactiveTrackColor,
    );
    final Paint activePaint =
        Paint()..color = activeTrackColorTween.evaluate(enableAnimation)!;
    final Paint inactivePaint =
        Paint()..color = inactiveTrackColorTween.evaluate(enableAnimation)!;

    final Rect leftTrackSegment = Rect.fromLTRB(
      trackRect.left,
      trackRect.top,
      thumbCenter.dx,
      trackRect.bottom,
    );
    final Rect rightTrackSegment = Rect.fromLTRB(
      thumbCenter.dx,
      trackRect.top,
      trackRect.right,
      trackRect.bottom,
    );

    final double trackRadius = trackRect.height / 2;
    context.canvas.drawRRect(
      RRect.fromRectAndRadius(leftTrackSegment, Radius.circular(trackRadius)),
      activePaint,
    );
    context.canvas.drawRRect(
      RRect.fromRectAndRadius(rightTrackSegment, Radius.circular(trackRadius)),
      inactivePaint,
    );
  }
}

/// Custom slider thumb shape that displays the value inside the thumb
class StepSliderThumbCircle extends SliderComponentShape {
  final double thumbRadius;
  final Color thumbColor;
  final double min;
  final double max;
  final TextStyle textStyle;

  StepSliderThumbCircle({
    required this.thumbRadius,
    required this.thumbColor,
    required this.min,
    required this.max,
    required this.textStyle,
  });

  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
    return Size.fromRadius(thumbRadius);
  }

  @override
  void paint(
    PaintingContext context,
    Offset center, {
    required Animation<double> activationAnimation,
    required Animation<double> enableAnimation,
    required bool isDiscrete,
    required TextPainter labelPainter,
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required TextDirection textDirection,
    required double value,
    required double textScaleFactor,
    required Size sizeWithOverflow,
  }) {
    final canvas = context.canvas;

    // Simple scaling based on activation animation
    // activationAnimation goes from 0.0 (normal) to 1.0 (pressed/dragging)
    final double scale =
        1.0 + (activationAnimation.value * 0.1); // Scale up to 1.1 when active
    final double currentRadius = thumbRadius * scale;

    // Draw white background circle
    final backgroundPaint =
        Paint()
          ..color = Colors.white
          ..style = PaintingStyle.fill;
    canvas.drawCircle(center, currentRadius, backgroundPaint);

    // Draw blue border
    final borderPaint =
        Paint()
          ..color = thumbColor
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.0;
    canvas.drawCircle(center, currentRadius - 1.0, borderPaint);

    // Draw text (value inside thumb) with blue color to match border
    final textSpan = TextSpan(
      text: '${(min + (value * (max - min))).round()}',
      style: textStyle.copyWith(
        color: thumbColor,
        fontSize: textStyle.fontSize! * scale, // Scale text with thumb
      ),
    );

    final tp = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: textDirection,
    );

    tp.layout();
    tp.paint(canvas, center - Offset(tp.width / 2, tp.height / 2));
  }
}
