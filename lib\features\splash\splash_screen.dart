import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:builder_app/features/login/presentation/bloc/app_config/config_bloc.dart';
import 'package:builder_app/features/login/presentation/bloc/app_config/config_state.dart';
import 'package:go_router/go_router.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  Widget build(BuildContext context) {
    return BlocListener<AppConfigBloc, ConfigState>(
      listener: (context, state) {
        if (state is UserLoggedInState) {
          GoRouter.of(context).go('/home');
        } else if (state is UserLoggedOutState) {
          GoRouter.of(context).go('/login');
        }
      },
      child: Scaffold(body: Center(child: CircularProgressIndicator())),
    );
  }
}
