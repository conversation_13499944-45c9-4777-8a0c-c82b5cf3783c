import 'dart:developer';

import 'package:flutter_dotenv/flutter_dotenv.dart';



class EnvironmentHelper {
  EnvironmentHelper._();

  // Environment flags
  static bool _isProduction = false;
  static bool _isStaging = false;
  static bool _isDevelopment = true;
  static String? _apiBaseUrl;
  static String _currentEnvironment = 'dev';

  // Getters
  static bool get isProduction => _isProduction;
  static bool get isStaging => _isStaging;
  static bool get isDevelopment => _isDevelopment;
  static String get apiBaseUrl => _apiBaseUrl ?? '';
  static String get currentEnvironment => _currentEnvironment;

  /// Initialize the app configuration
static Future<void> init() async {
  try {
    log('Initializing AppConfig');

    _currentEnvironment = const String.fromEnvironment('ENVIRONMENT', defaultValue: 'dev');

    await dotenv.load(fileName: 'assets/.env');

    final prefix = _currentEnvironment.toUpperCase();
    _isProduction = _currentEnvironment == 'prod';
    _isStaging = _currentEnvironment == 'staging';
    _isDevelopment = _currentEnvironment == 'dev';

    _apiBaseUrl = dotenv.env['${prefix}_BASE_URL'];
    log('API Base URL: $_apiBaseUrl');
  } catch (e) {
    log('Error initializing AppConfig: $e');
  }
}

}
