import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:graphic/graphic.dart' as graphic;
import 'ui_controls_library/ui_controls_library/lib/widgets/ui_builder/flexible_widget_serializer.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Graphic Serialization Test',
      home: GraphicSerializationTest(),
    );
  }
}

class GraphicSerializationTest extends StatefulWidget {
  @override
  _GraphicSerializationTestState createState() => _GraphicSerializationTestState();
}

class _GraphicSerializationTestState extends State<GraphicSerializationTest> {
  String jsonOutput = '';
  Widget? deserializedWidget;
  bool testPassed = false;
  String testResult = '';

  @override
  void initState() {
    super.initState();
    _runTest();
  }

  void _runTest() {
    try {
      print("=== Starting Graphic Chart Serialization Test ===");
      
      // Create test data
      final data = [
        {'x': 5.0, 'y': 3000.0, 'index': 0},
        {'x': 15.0, 'y': 5000.0, 'index': 1},
        {'x': 25.0, 'y': 7000.0, 'index': 2},
      ];

      // Create a graphic Chart widget
      final graphicChart = Container(
        height: 300,
        child: graphic.Chart(
          data: data,
          variables: {
            'x': graphic.Variable(
              accessor: (Map map) => map['x'] as num,
              scale: graphic.LinearScale(min: 0, max: 35, tickCount: 8),
            ),
            'y': graphic.Variable(
              accessor: (Map map) => map['y'] as num,
              scale: graphic.LinearScale(min: 2000, max: 8000, tickCount: 7),
            ),
            'index': graphic.Variable(
              accessor: (Map map) => map['index'] as num,
            ),
          },
          marks: [
            graphic.PointMark(
              position: graphic.Varset('x') * graphic.Varset('y'),
              size: graphic.SizeEncode(value: 8.0),
              color: graphic.ColorEncode(
                variable: 'index',
                values: [Colors.blue, Colors.red, Colors.green],
              ),
              shape: graphic.ShapeEncode(value: graphic.CircleShape()),
            ),
          ],
          axes: [
            graphic.Defaults.horizontalAxis,
            graphic.Defaults.verticalAxis,
          ],
          coord: graphic.RectCoord(),
        ),
      );
      
      // Test serialization
      final serialized = FlexibleWidgetSerializer.serialize(graphicChart);
      final jsonString = const JsonEncoder.withIndent('  ').convert(serialized);
      
      print("=== Serialized Graphic Chart JSON ===");
      print(jsonString);
      
      // Check if serialization worked correctly
      final childSerialized = serialized['child'] as Map<String, dynamic>?;
      if (childSerialized != null && childSerialized['type'] == 'GraphicChart') {
        print("✅ SUCCESS: Graphic Chart serialized correctly as GraphicChart type");
        testPassed = true;
        testResult = "SUCCESS: Serialization working correctly";
        
        // Test deserialization
        final rebuiltWidget = FlexibleWidgetSerializer.deserialize(serialized);
        if (rebuiltWidget != null) {
          print("✅ SUCCESS: Deserialization also working");
          setState(() {
            deserializedWidget = rebuiltWidget;
            jsonOutput = jsonString;
          });
        } else {
          print("❌ ERROR: Deserialization failed");
          testResult = "ERROR: Deserialization failed";
        }
      } else {
        print("❌ ERROR: Graphic Chart not serialized correctly");
        print("Expected type: GraphicChart");
        print("Actual child: $childSerialized");
        testResult = "ERROR: Serialization failed - Chart not recognized as GraphicChart";
      }
      
      setState(() {
        jsonOutput = jsonString;
      });
      
    } catch (e, stackTrace) {
      print("❌ ERROR in test: $e");
      print("Stack trace: $stackTrace");
      setState(() {
        testResult = "ERROR: $e";
        jsonOutput = 'Error: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Graphic Serialization Test'),
        backgroundColor: testPassed ? Colors.green : Colors.red,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Test Result
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: testPassed ? Colors.green.shade100 : Colors.red.shade100,
                border: Border.all(
                  color: testPassed ? Colors.green : Colors.red,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                testResult,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: testPassed ? Colors.green.shade800 : Colors.red.shade800,
                ),
              ),
            ),
            
            SizedBox(height: 24),
            
            // JSON Output
            if (jsonOutput.isNotEmpty) ...[
              Text(
                'Serialized JSON:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Container(
                width: double.infinity,
                height: 300,
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey.shade50,
                ),
                child: SingleChildScrollView(
                  child: SelectableText(
                    jsonOutput,
                    style: TextStyle(fontSize: 12, fontFamily: 'Courier'),
                  ),
                ),
              ),
              SizedBox(height: 24),
            ],
            
            // Deserialized Widget
            if (deserializedWidget != null) ...[
              Text(
                'Deserialized Widget:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Container(
                width: double.infinity,
                height: 350,
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.green),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: deserializedWidget!,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
