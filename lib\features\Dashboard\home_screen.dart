

import 'package:builder_app/features/dashboard/clean_runtime_demo.dart';
import 'package:builder_app/features/dashboard/pie_chart_screen.dart';
import 'package:builder_app/features/dashboard/date_range_demo.dart';
import 'package:builder_app/features/login/presentation/bloc/home/<USER>';
import 'package:builder_app/features/login/presentation/bloc/home/<USER>';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:responsive_framework/responsive_framework.dart';
import 'package:builder_app/features/login/presentation/bloc/app_config/config_bloc.dart';
import 'package:builder_app/features/login/presentation/bloc/app_config/config_event.dart';


import 'package:ui_controls_library/ui_controls_library.dart' as ui_controls;



class HomeScreen extends StatefulWidget {
  final String? welcomeMessage;
  final Map<String, dynamic>? userData;
  final int? selectedTab;
  final Map<String, dynamic>? settingsData;

  const HomeScreen({
    super.key,
    this.welcomeMessage,
    this.userData,
    this.selectedTab,
    this.settingsData,
  });

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {


  int selectedIndex = 0;

 late ui_controls.ChartSizeConfig selectedConfig;

  Widget _buildResponsiveBody() {
    switch (selectedIndex) {
      case 0:
        return RingPieChartDemoScreen();
      case 1:
        return CleanRuntimeDemo();
      case 2:
        return const DateRangeDemoScreen();
      case 3:
        return const Center(child: Text("Border Radius UI Coming Soon"));
      default:
        return const Center(child: Text("Select a section"));
    }
  }

  void _onSidebarItemTapped(int index) {
    setState(() {
      selectedIndex = index;
    });
  }
@override
  void initState() {
    super.initState();
  }

//  @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: const Color(0xFFF8FBFF),
//       body: Padding(
//         padding: const EdgeInsets.all(20),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             const Text(
//               'Properties - Typography',
//               style: TextStyle(fontSize: 28, fontWeight: FontWeight.bold),
//             ),
//             const SizedBox(height: 8),
//             const Text(
//               'Small',
//               style: TextStyle(fontSize: 18),
//             ),
//             const SizedBox(height: 20),
//             Container(
//               width: 400,
//               decoration: BoxDecoration(
//                 borderRadius: BorderRadius.circular(12),
//                 color: Colors.white,
//                 boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 4)],
//               ),
//               padding: const EdgeInsets.all(16),
//               child: SfCartesianChart(
//                 enableAxisAnimation: false,
//                 title: ChartTitle(text: 'Temperature theme'),
//                 legend: const Legend(
//                   isVisible: true,
//                   position: LegendPosition.top,
//                   overflowMode: LegendItemOverflowMode.wrap,
//                 ),
//               primaryXAxis: NumericAxis(
//   minimum: 10,
//   maximum: 60,
//   interval: 10,
//   axisLine: const AxisLine(width: 0),           // hides the axis line
//   majorTickLines: const MajorTickLines(size: 0), // hides the ticks
//   labelStyle: const TextStyle(color: Colors.black), // labels visible
// ),

// primaryYAxis: NumericAxis(
//   minimum: 0,
//   maximum: 50,
//   interval: 10,
//   axisLine: const AxisLine(width: 0),
//   majorTickLines: const MajorTickLines(size: 0),
//   labelStyle: const TextStyle(color: Colors.black),
// ),

//                 series: <BubbleSeries<_ChartData, num>>[
//                   BubbleSeries<_ChartData, num>(
//                     dataSource: _getExactBubbleData(),
//                     xValueMapper: (_ChartData data, _) => data.x,
//                     yValueMapper: (_ChartData data, _) => data.y,
//                     sizeValueMapper: (_ChartData data, _) => data.size,
//                     name: 'Group1',
//                     color: Colors.blue.withOpacity(0.5),
//                     opacity: 0.9,
//                     minimumRadius: 2,
//                     maximumRadius: 4,
//                   ),
//                 ],
//               ),
//             ),
//             const SizedBox(height: 20),
//             const Text(
//               'Properties',
//               style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
//             ),
//             const SizedBox(height: 8),
//             const Text(
//               'Heading 1 medium 14',
//               style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
//             ),
//             const SizedBox(height: 4),
//             const Text(
//               'Body 1 Regular 12',
//               style: TextStyle(fontSize: 12),
//             ),
//             const SizedBox(height: 4),
//             const Text(
//               'Label - Regular 10',
//               style: TextStyle(fontSize: 10),
//             ),
//           ],
//         ),
//       ),
//     );
//   }

// List<_ChartData> _getExactBubbleData() {
//   return [
//     _ChartData(20, 10, 0.2),
//     _ChartData(15, 15, 0.3),
//     _ChartData(15, 20, 0.2),
//     _ChartData(20, 25, 0.1),
//     _ChartData(25, 35, 4),
//     _ChartData(23, 35, 4),
//     _ChartData(12, 35, 4),
//     _ChartData(20, 30, 3),
//     _ChartData(30, 18, 1),
//     _ChartData(35, 35, 1),
//     _ChartData(40, 10, 5),
//     _ChartData(45, 28, 0.2),
//     _ChartData(50, 20, 0.1),
//     _ChartData(55, 40, 1),
//   ];
// }

// }

// class _ChartData {
//   final double x;
//   final double y;
//   final double size;

//   _ChartData(this.x, this.y, this.size);
// }

  @override
  Widget build(BuildContext context) {
    final isMobile = ResponsiveBreakpoints.of(context).isMobile;

    final sidebar = isMobile
        ? _SidebarDrawer(onTap: _onSidebarItemTapped)
        : _SidebarRail(onTap: _onSidebarItemTapped);

    final body = _buildResponsiveBody();
    // Column(
    //   children: [
    //     Expanded(
    //       child: Row(
    //         children: [
    //           Expanded(
    //             child: _buildResponsiveBody(),
    //           ),
             
    //         ],
    //       ),
    //     ),
    //     // Padding(
    //     //   padding: const EdgeInsets.all(8.0),
    //     //   child: CommonButton(
    //     //     text: 'Elevated Action',
    //     //     tooltip: 'Perform an action',
    //     //     onPressed: () async {
    //     //       if (selectedIndex == 0) {
    //     //         log("=================");
    //     //         final jsonOutput = await typographyKey.currentState?.generateJson();
    //     //         if (jsonOutput != null && context.mounted) {
    //     //           log("Serialized PieChartData: $jsonOutput");
    //     //           context.go("/json", extra: jsonOutput);
    //     //         }
    //     //       }
    //     //       if (selectedIndex == 1) {
    //     //         final jsonOutput = await borderThicknessKey.currentState?.generateJson();
    //     //         log("Serialized Border Thickness: $jsonOutput");
    //     //       }
    //     //     },
    //     //     child: const Text("Next", style: TextStyle(color: Colors.white)),
    //     //   ),
    //     // ),
    //   ],
    // );

    return BlocProvider(
      create: (context) => HomeBloc()..add(FetchDropdownA()),
      child: isMobile
          ? Scaffold(
              appBar: AppBar(
                title: Text(widget.welcomeMessage ?? 'Bloc'),
                leading: Builder(
                  builder: (context) => IconButton(
                    icon: const Icon(Icons.menu),
                    onPressed: () => Scaffold.of(context).openDrawer(),
                  ),
                ),
              ),
              drawer: sidebar,
              body: body,
            )
          : Scaffold(
              body: Row(
                children: [
                  SizedBox(width: 48, child: sidebar),
                  const VerticalDivider(width: 0.5),
                  Expanded(child: body),
                ],
              ),
            ),
    );
  }
}


class _SidebarDrawer extends StatelessWidget {
  final void Function(int) onTap;

  const _SidebarDrawer({required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Column(
        children: [
          const SizedBox(height: 16),
          ListTile(
            leading: const Icon(Icons.home),
            title: const Text("Typography"),
            onTap: () {
              Navigator.pop(context);
              onTap(0);
            },
          ),
          ListTile(
            leading: const Icon(Icons.person),
            title: const Text("Border Thickness"),
            onTap: () {
              Navigator.pop(context);
              onTap(1);
            },
          ),
          ListTile(
            leading: const Icon(Icons.date_range),
            title: const Text("Date Range"),
            onTap: () {
              Navigator.pop(context);
              onTap(2);
            },
          ),
          ListTile(
            leading: const Icon(Icons.settings),
            title: const Text("Border Radius"),
            onTap: () {
              Navigator.pop(context);
              onTap(3);
            },
          ),
          ListTile(
            leading: const Icon(Icons.logout),
            title: const Text("Logout"),
            onTap: () {
              Navigator.pop(context);
              context.read<AppConfigBloc>().add(LogoutEvent());
            },
          ),
        ],
      ),
    );
  }
}

class _SidebarRail extends StatelessWidget {
  final void Function(int) onTap;

  const _SidebarRail({required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 48,
      color: Theme.of(context).colorScheme.primary.withAlpha(30),
      child: Column(
        children: [
          const SizedBox(height: 16),
          IconButton(
            icon: const Icon(Icons.pie_chart, size: 20),
            tooltip: 'Properties',
            onPressed: () => onTap(0),
          ),
          const SizedBox(height: 8),
          IconButton(
            icon: const Icon(Icons.calendar_month, size: 20),
            tooltip: 'Calendar',
            onPressed: () => onTap(1),
          ),
          const SizedBox(height: 8),
          IconButton(
            icon: const Icon(Icons.date_range, size: 20),
            tooltip: 'Date Range',
            onPressed: () => onTap(2),
          ),
          const SizedBox(height: 8),
          IconButton(
            icon: const Icon(Icons.settings, size: 20),
            tooltip: 'Border Radius',
            onPressed: () => onTap(3),
          ),
          const SizedBox(height: 8),
          IconButton(
            icon: const Icon(Icons.logout, size: 20),
            tooltip: 'Logout',
            onPressed: () {
              context.read<AppConfigBloc>().add(LogoutEvent());
            },
          ),
          const Spacer(),
        ],
      ),
    );
  }
}





