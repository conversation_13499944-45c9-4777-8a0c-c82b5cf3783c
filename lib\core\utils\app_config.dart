import 'package:builder_app/core/utils/environment_helper.dart';

class AppConfig {
  static String get baseUrl => EnvironmentHelper.apiBaseUrl;
  static const String appVersion = '1.0.0';

  // Environment detection
  static String get currentEnvironment => EnvironmentHelper.currentEnvironment;
  static bool get isDevelopment => EnvironmentHelper.isDevelopment;
  static bool get isStaging => EnvironmentHelper.isStaging;
  static bool get isProduction => EnvironmentHelper.isProduction;
} 
