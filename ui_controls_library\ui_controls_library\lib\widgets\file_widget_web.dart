import 'dart:convert';
// ignore: deprecated_member_use
import 'dart:html' as html;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_svg/svg.dart';
import '../utils/callback_interpreter.dart';
import 'utils/file_widget_json_parser.dart';
import 'components/file_widget_components.dart';

/// Web-specific implementation of FileWidget
class FileWidgetWeb extends StatefulWidget {
  // Basic properties
  final bool isRequired;
  final bool allowMultiple;
  final List<String>? allowedExtensions;
  final FileType fileType;
  final int? maxFileSizeBytes;
  final int? maxFiles;

  // Appearance properties
  final Color textColor;
  final Color backgroundColor;
  final Color borderColor;
  final double borderWidth;
  final double borderRadius;
  final bool hasBorder;
  final double fontSize;
  final FontWeight fontWeight;
  final bool isCompact;
  final bool hasShadow;
  final double elevation;
  final bool isDarkTheme;
  final TextAlign textAlign;

  // Label properties
  final String? label;
  final String? hint;
  final String? helperText;
  final String? errorText;
  final String buttonText;

  // Icon properties
  final bool showIcon;
  final IconData? icon;

  // Behavior properties
  final bool isReadOnly;
  final bool isDisabled;
  final bool showFileName;
  final bool showFileSize;
  final bool showFileType;
  final bool showClearButton;
  final bool showPreview;
  final bool uploadImmediately;
  final bool showProgressBar;
  final bool allowDragDrop;

  // Layout properties
  final double width;
  final double height;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;

  // Callbacks
  final Function(List<PlatformFile>)? onFilesSelected;
  final Function()? onClear;
  final Function(List<PlatformFile>)? onUpload;
  final Function(PlatformFile)? onViewFile;
  final Function(PlatformFile)? onOpenFile;
  final Function()? onCancelUpload;

  // Advanced interaction properties
  final void Function(bool)? onHover;
  final void Function(bool)? onFocus;
  final FocusNode? focusNode;
  final Color? hoverColor;
  final Color? focusColor;
  final bool enableFeedback;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;

  // JSON configuration properties
  final Map<String, dynamic>? jsonCallbacks;
  final bool useJsonCallbacks;
  final Map<String, dynamic>? callbackState;
  final Map<String, Function>? customCallbackHandlers;
  final Map<String, dynamic>? jsonConfig;
  final bool useJsonValidation;
  final bool useJsonStyling;
  final bool useJsonFormatting;

  // File-specific JSON configuration
  final bool useJsonFileHandling;
  final Map<String, dynamic>? fileHandlingConfig;

  const FileWidgetWeb({
    super.key,
    this.isRequired = false,
    this.allowMultiple = false,
    this.allowedExtensions,
    this.fileType = FileType.any,
    this.maxFileSizeBytes,
    this.maxFiles,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = const Color(0xFFCCCCCC),
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.hasBorder = true,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.isCompact = false,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.isDarkTheme = false,
    this.textAlign = TextAlign.start,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.buttonText = 'Choose File',
    this.showIcon = true,
    this.icon = Icons.upload_file,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.showFileName = true,
    this.showFileSize = true,
    this.showFileType = true,
    this.showClearButton = true,
    this.showPreview = false,
    this.uploadImmediately = false,
    this.showProgressBar = false,
    this.allowDragDrop = false,
    this.width = double.infinity,
    this.height = 0,
    this.padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
    this.margin = const EdgeInsets.all(0),
    this.onFilesSelected,
    this.onClear,
    this.onUpload,
    this.onViewFile,
    this.onOpenFile,
    this.onCancelUpload,
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonValidation = false,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    this.useJsonFileHandling = false,
    this.fileHandlingConfig,
  });

  @override
  State<FileWidgetWeb> createState() => _FileWidgetWebState();
}

// Enum for file upload states
enum FileUploadState { defaultState, uploading, uploaded, completed }

class _FileWidgetWebState extends State<FileWidgetWeb> {
  List<PlatformFile> _selectedFiles = [];
  String? _errorText;
  bool _isDragging = false;
  double _uploadProgress = 0.0;
  bool _isUploading = false;
  FileUploadState _uploadState = FileUploadState.defaultState;
  bool _isHovered = false;
  bool _hasFocus = false;

  final hoverIconColor = const Color(0xFF0058FF);
  final defaultIconColor = const Color(0xFFCCCCCC);

  @override
  void initState() {
    super.initState();
    _errorText = widget.errorText;
  }

  void _onHoverChange(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }
    });
  }

  void _onFocusChange(bool hasFocus) {
    setState(() {
      _hasFocus = hasFocus;
      if (widget.onFocus != null) {
        widget.onFocus!(hasFocus);
      }
    });
  }

  Future<void> _pickFiles() async {
    if (widget.isDisabled || widget.isReadOnly) return;

    try {
      final result = await FilePicker.platform.pickFiles(
        type: widget.fileType,
        allowMultiple: widget.allowMultiple,
        allowedExtensions:
            widget.fileType == FileType.custom
                ? widget.allowedExtensions
                : null,
      );

      if (result != null && result.files.isNotEmpty) {
        setState(() {
          _selectedFiles = result.files;
          _errorText = null;
        });

        if (widget.onFilesSelected != null) {
          widget.onFilesSelected!(_selectedFiles);
        }

        // Start upload automatically
        _uploadFiles();
      }
    } catch (e) {
      setState(() {
        _errorText = 'Error picking files: $e';
      });
    }
  }

  void _clearFiles() {
    setState(() {
      _selectedFiles = [];
      _errorText = widget.errorText;
      _uploadProgress = 0.0;
      _isUploading = false;
      _uploadState = FileUploadState.defaultState;
    });

    if (widget.onClear != null) {
      widget.onClear!();
    }
  }

  void _cancelUpload() {
    if (!_isUploading) return;

    setState(() {
      _isUploading = false;
      _uploadProgress = 0.0;
      _uploadState = FileUploadState.defaultState;
    });

    if (widget.onCancelUpload != null) {
      widget.onCancelUpload!();
    }
  }

  Future<void> _uploadFiles() async {
    if (_selectedFiles.isEmpty || widget.isDisabled || widget.isReadOnly) {
      return;
    }

    setState(() {
      _isUploading = true;
      _uploadProgress = 0.0;
      _uploadState = FileUploadState.uploading;
    });

    try {
      // Simulate upload progress
      for (int i = 0; i <= 20; i++) {
        if (!_isUploading) return;

        await Future.delayed(const Duration(milliseconds: 100));

        if (!_isUploading) return;

        final progress = i / 20;
        setState(() {
          _uploadProgress = progress;
        });
      }

      // Show uploaded state (100% complete)
      setState(() {
        _uploadState = FileUploadState.uploaded;
      });

      // Show uploaded state briefly before finishing
      await Future.delayed(const Duration(milliseconds: 1500));

      if (!_isUploading) return;

      setState(() {
        _isUploading = false;
        _uploadState = FileUploadState.completed;
      });

      if (widget.onUpload != null) {
        widget.onUpload!(_selectedFiles);
      }
    } catch (e) {
      setState(() {
        _isUploading = false;
        _errorText = 'Upload failed: $e';
      });
    }
  }

  String _getMimeType(String? extension) {
    if (extension == null) return 'application/octet-stream';

    switch (extension.toLowerCase()) {
      case 'pdf':
        return 'application/pdf';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case 'xls':
        return 'application/vnd.ms-excel';
      case 'xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case 'ppt':
        return 'application/vnd.ms-powerpoint';
      case 'pptx':
        return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'bmp':
        return 'image/bmp';
      case 'svg':
        return 'image/svg+xml';
      case 'mp4':
        return 'video/mp4';
      case 'avi':
        return 'video/x-msvideo';
      case 'mov':
        return 'video/quicktime';
      case 'mp3':
        return 'audio/mpeg';
      case 'wav':
        return 'audio/wav';
      case 'zip':
        return 'application/zip';
      case 'rar':
        return 'application/vnd.rar';
      case 'txt':
        return 'text/plain';
      case 'html':
        return 'text/html';
      case 'css':
        return 'text/css';
      case 'js':
        return 'application/javascript';
      case 'json':
        return 'application/json';
      case 'xml':
        return 'application/xml';
      default:
        return 'application/octet-stream';
    }
  }

  Future<void> _openFileInNewWindow(PlatformFile file) async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Opening file: ${file.name}'),
          duration: const Duration(seconds: 2),
        ),
      );

      if (file.bytes != null) {
        // Create blob URL from file bytes with proper MIME type
        final mimeType = _getMimeType(file.extension);
        final blob = html.Blob([file.bytes!], mimeType);
        final url = html.Url.createObjectUrlFromBlob(blob);

        // Open in new tab/window
        html.window.open(url, '_blank');

        // Clean up the blob URL after a delay
        Future.delayed(const Duration(seconds: 5), () {
          html.Url.revokeObjectUrl(url);
        });

        print('Successfully opened file in new tab: ${file.name}');
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Cannot open file: ${file.name} (no file data available)',
            ),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error opening file: $e'),
          duration: const Duration(seconds: 3),
          backgroundColor: Colors.red,
        ),
      );
      print('Error opening file: $e');
    }
  }

  void _viewFile(PlatformFile file) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.description, color: Colors.blue),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'File Details',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                ),
              ),
            ],
          ),
          content: Container(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDetailRow('Name', file.name),
                const SizedBox(height: 8),
                _buildDetailRow('Size', _formatFileSize(file.size)),
                const SizedBox(height: 8),
                if (file.extension != null)
                  _buildDetailRow('Type', file.extension!.toUpperCase()),
                if (file.extension != null) const SizedBox(height: 8),
                _buildDetailRow(
                  'Last Modified',
                  DateTime.now().toString().split('.')[0],
                ),
                const SizedBox(height: 16),
                Center(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getFileIcon(file.extension),
                      size: 48,
                      color: Colors.blue.shade600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Close'),
            ),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                _openFileInNewWindow(file);
              },
              icon: Icon(Icons.open_in_new, size: 16),
              label: Text('Open'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            '$label:',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade700,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(color: Colors.black87),
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
          ),
        ),
      ],
    );
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024)
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  Color _getFileIconColor(String? extension) {
    if (extension == null) return const Color(0xFF666666);

    switch (extension.toLowerCase()) {
      case 'pdf':
        return const Color(0xFFD32F2F);
      case 'doc':
      case 'docx':
        return const Color(0xFF1976D2);
      case 'xls':
      case 'xlsx':
        return const Color(0xFF388E3C);
      case 'ppt':
      case 'pptx':
        return const Color(0xFFFF5722);
      case 'txt':
        return const Color(0xFF757575);
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
        return const Color(0xFF9C27B0);
      case 'mp4':
      case 'avi':
      case 'mov':
        return const Color(0xFFE91E63);
      case 'mp3':
      case 'wav':
      case 'flac':
        return const Color(0xFF4CAF50);
      case 'zip':
      case 'rar':
      case '7z':
        return const Color(0xFF795548);
      case 'html':
        return const Color(0xFFFF5722);
      case 'css':
        return const Color(0xFF2196F3);
      case 'js':
        return const Color(0xFFFFC107);
      case 'json':
        return const Color(0xFF607D8B);
      default:
        return const Color(0xFF666666);
    }
  }

  IconData _getFileIcon(String? extension) {
    if (extension == null) return Icons.insert_drive_file;

    switch (extension.toLowerCase()) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      case 'txt':
        return Icons.text_snippet;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
        return Icons.image;
      case 'mp4':
      case 'avi':
      case 'mov':
        return Icons.video_file;
      case 'mp3':
      case 'wav':
      case 'flac':
        return Icons.audio_file;
      case 'zip':
      case 'rar':
      case '7z':
        return Icons.folder_zip;
      case 'html':
        return Icons.web;
      case 'css':
        return Icons.style;
      case 'js':
        return Icons.code;
      case 'json':
        return Icons.data_object;
      default:
        return Icons.insert_drive_file;
    }
  }

  double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1920) return 18.0;
    if (screenWidth >= 1440) return 16.0;
    if (screenWidth >= 1280) return 14.0;
    if (screenWidth >= 768) return 12.0;
    return 10.0;
  }

  double _getResponsiveHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1920) return 56.0;
    if (screenWidth >= 1440) return 48.0;
    if (screenWidth >= 1280) return 40.0;
    if (screenWidth >= 768) return 32.0;
    return 32.0;
  }

  EdgeInsets _getResponsivePadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth >= 1440)
      return const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0);
    if (screenWidth >= 1280)
      return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 3.0);
    if (screenWidth >= 768)
      return const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0);
    return const EdgeInsets.symmetric(horizontal: 6.0, vertical: 1.0);
  }

  double _getResponsiveIconSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1920) return 22.0;
    if (screenWidth >= 1440) return 20.0;
    if (screenWidth >= 1280) return 18.0;
    if (screenWidth >= 768) return 15.0;
    return 15.0;
  }

  Widget _buildDragDropArea(Widget child) {
    if (!widget.allowDragDrop) return child;

    return DragTarget<List<PlatformFile>>(
      onWillAccept: (data) => !widget.isDisabled && !widget.isReadOnly,
      onAccept: (files) {
        setState(() {
          _selectedFiles = files;
          _isDragging = false;
        });
        if (widget.onFilesSelected != null) {
          widget.onFilesSelected!(files);
        }
      },
      onLeave: (data) {
        setState(() {
          _isDragging = false;
        });
      },
      builder: (context, candidateData, rejectedData) {
        return Container(
          decoration: BoxDecoration(
            border:
                _isDragging ? Border.all(color: Colors.blue, width: 2) : null,
            borderRadius: BorderRadius.circular(widget.borderRadius),
          ),
          child: child,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget content;

    switch (_uploadState) {
      case FileUploadState.defaultState:
        content = _buildDefaultState();
        break;
      case FileUploadState.uploading:
        content = _buildUploadingState();
        break;
      case FileUploadState.uploaded:
        content = _buildUploadedState();
        break;
      case FileUploadState.completed:
        content = _buildCompletedState();
        break;
    }

    final dragDropContent = _buildDragDropArea(content);

    final shadowWidget =
        widget.hasShadow
            ? Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(25),
                    blurRadius: widget.elevation,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: dragDropContent,
            )
            : dragDropContent;

    return Container(
      width: widget.width,
      margin: widget.margin,
      child: shadowWidget,
    );
  }

  Widget _buildDefaultState() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: _getResponsiveHeight(context),
          child: ElevatedButton.icon(
            onPressed:
                widget.isDisabled || widget.isReadOnly ? null : _pickFiles,
            icon:
                widget.showIcon && widget.icon != null
                    ? Icon(
                      widget.icon,
                      color: Colors.white,
                      size: _getResponsiveIconSize(context),
                    )
                    : const SizedBox.shrink(),
            label: Text(
              'Upload ',
              style: TextStyle(
                fontSize: _getResponsiveFontSize(context),
                fontWeight: FontWeight.w400,
                color: Colors.white,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF0058FF),
              padding: _getResponsivePadding(context),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4.0),
              ),
              elevation: 0,
            ),
          ),
        ),
        if (_errorText != null) ...[
          const SizedBox(height: 8),
          Text(
            _errorText!,
            style: TextStyle(
              color: Colors.red,
              fontSize: _getResponsiveFontSize(context) * 0.8,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildUploadingState() {
    final effectiveBackgroundColor =
        widget.isDarkTheme ? Colors.grey.shade800 : widget.backgroundColor;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        MouseRegion(
          onEnter: (_) => _onHoverChange(true),
          onExit: (_) => _onHoverChange(false),
          cursor: SystemMouseCursors.click,
          child: Focus(
            focusNode: widget.focusNode,
            onFocusChange: _onFocusChange,
            child: Container(
              height: _getResponsiveHeight(context),
              decoration: BoxDecoration(
                color: effectiveBackgroundColor,
                borderRadius: BorderRadius.circular(widget.borderRadius),
                border:
                    widget.hasBorder
                        ? Border.all(
                          color:
                              _isHovered
                                  ? (widget.hoverColor ??
                                      const Color(0xFF0058FF))
                                  : _hasFocus
                                  ? (widget.focusColor ??
                                      const Color(0xFF0058FF))
                                  : widget.borderColor,
                          width: widget.borderWidth,
                        )
                        : null,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(
                                top: 12.0,
                                left: 12.0,
                              ),
                              child: Text(
                                'Uploading (${_selectedFiles.length})',
                                style: TextStyle(
                                  fontSize:
                                      _getResponsiveFontSize(context) * 0.9,
                                  fontWeight: FontWeight.w500,
                                  color: const Color(0xFF333333),
                                ),
                              ),
                            ),
                            GestureDetector(
                              onTap: _cancelUpload,
                              child: Container(
                                padding: const EdgeInsets.only(
                                  right: 12.0,
                                  top: 5.0,
                                ),
                                child: Icon(
                                  Icons.close,
                                  size: _getResponsiveIconSize(context),
                                  color: defaultIconColor,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 3),
                        LinearProgressIndicator(
                          value: _uploadProgress,
                          backgroundColor: Colors.grey.shade300,
                          valueColor: const AlwaysStoppedAnimation<Color>(
                            Colors.blue,
                          ),
                          minHeight: _getResponsiveProgressBarHeight(context),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUploadedState() {
    final effectiveBackgroundColor =
        widget.isDarkTheme ? Colors.grey.shade800 : widget.backgroundColor;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        MouseRegion(
          onEnter: (_) => _onHoverChange(true),
          onExit: (_) => _onHoverChange(false),
          cursor: SystemMouseCursors.click,
          child: Focus(
            focusNode: widget.focusNode,
            onFocusChange: _onFocusChange,
            child: Container(
              height: _getResponsiveHeight(context),
              decoration: BoxDecoration(
                color: effectiveBackgroundColor,
                borderRadius: BorderRadius.circular(widget.borderRadius),
                border:
                    widget.hasBorder
                        ? Border.all(
                          color:
                              _isHovered
                                  ? (widget.hoverColor ??
                                      const Color(0xFF0058FF))
                                  : _hasFocus
                                  ? (widget.focusColor ??
                                      const Color(0xFF0058FF))
                                  : widget.borderColor,
                          width: widget.borderWidth,
                        )
                        : null,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(
                            top: 12.0,
                            left: 12.0,
                            right: 12.0,
                          ),
                          child: Text(
                            'Uploaded 100%',
                            style: TextStyle(
                              fontSize: _getResponsiveFontSize(context) * 0.9,
                              fontWeight: FontWeight.w500,
                              color: const Color(0xFF999999),
                            ),
                          ),
                        ),
                        const SizedBox(height: 3),
                        LinearProgressIndicator(
                          value: 1.0,
                          backgroundColor: Colors.green.shade200,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.green.shade600,
                          ),
                          minHeight: _getResponsiveProgressBarHeight(context),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCompletedState() {
    final effectiveBackgroundColor =
        widget.isDarkTheme ? Colors.grey.shade800 : widget.backgroundColor;

    if (_selectedFiles.isEmpty) {
      return _buildDefaultState();
    }

    final file = _selectedFiles.first;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        MouseRegion(
          onEnter: (_) => _onHoverChange(true),
          onExit: (_) => _onHoverChange(false),
          cursor: SystemMouseCursors.click,
          child: Focus(
            focusNode: widget.focusNode,
            onFocusChange: _onFocusChange,
            child: Container(
              height: _getResponsiveHeight(context),
              padding: _getResponsivePadding(context),
              decoration: BoxDecoration(
                color: effectiveBackgroundColor,
                borderRadius: BorderRadius.circular(widget.borderRadius),
                border:
                    widget.hasBorder
                        ? Border.all(
                          color:
                              _isHovered
                                  ? (widget.hoverColor ??
                                      const Color(0xFF0058FF))
                                  : _hasFocus
                                  ? (widget.focusColor ??
                                      const Color(0xFF0058FF))
                                  : widget.borderColor,
                          width: widget.borderWidth,
                        )
                        : null,
              ),
              child: Row(
                children: [
                  // File type icon
                  Icon(
                    _getFileIcon(file.extension),
                    size: _getResponsiveIconSize(context),
                    color: _getFileIconColor(file.extension),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      file.name,
                      style: TextStyle(
                        fontSize: _getResponsiveFontSize(context) * 0.9,
                        color: const Color(0xFF333333),
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Navigate icon (opens file in new window)
                  GestureDetector(
                    onTap: () => _openFileInNewWindow(file),
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      // child: Icon(
                      //   Icons.open_in_new,
                      //   size: _getResponsiveIconSize(context),
                      //   color: _isHovered ? hoverIconColor : defaultIconColor,
                      // ),
                      child: SvgPicture.asset(
                        _isHovered
                            ? 'assets/images/maxmize-hover.svg'
                            : 'assets/images/maxmize.svg',
                        package: 'ui_controls_library',
                        width: _getResponsiveIconSize(context),
                      ),
                    ),
                  ),
                  const SizedBox(width: 4),
                  // Eye icon (views the file)
                  GestureDetector(
                    onTap: () => _viewFile(file),
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      // child: Icon(
                      //   Icons.visibility,
                      //   size: _getResponsiveIconSize(context),
                      //   color: _isHovered ? hoverIconColor : defaultIconColor,
                      // ),
                      child: SvgPicture.asset(
                        _isHovered
                            ? 'assets/images/eye-hover.svg'
                            : 'assets/images/eye.svg',
                        package: 'ui_controls_library',
                        width: _getResponsiveIconSize(context),
                      ),
                    ),
                  ),
                  const SizedBox(width: 4),
                  // Delete icon (deletes file and returns to default state)
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedFiles.clear();
                        _uploadState = FileUploadState.defaultState;
                        _uploadProgress = 0.0;
                        _isUploading = false;
                      });
                      if (widget.onClear != null) {
                        widget.onClear!();
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      // child: Icon(
                      //   Icons.delete_outline,
                      //   size: _getResponsiveIconSize(context),
                      //   color: _isHovered ? hoverIconColor : defaultIconColor,
                      // ),
                      child: SvgPicture.asset(
                        _isHovered
                            ? 'assets/images/trash-hover.svg'
                            : 'assets/images/trash.svg',
                        package: 'ui_controls_library',
                        width: _getResponsiveIconSize(context),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}

double _getResponsiveProgressBarHeight(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 8.5; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 8.5; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 4.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 4.0; // Small (768-1024px)
  } else {
    return 4.0; // Default for very small screens
  }
}