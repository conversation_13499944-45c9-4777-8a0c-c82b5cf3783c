import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// A class that provides localized strings for the application
class AppLocalizations {
  final Locale locale;
  Map<String, dynamic> _localizedStrings = {};

  AppLocalizations(this.locale);

  // Global instance for accessing localization without BuildContext
  static AppLocalizations? _instance;
  
  // Getter for the global instance
  static AppLocalizations? get instance => _instance;
  
  // Setter for the global instance
  static set instance(AppLocalizations? value) {
    _instance = value;
  }

  // Helper method to keep the code in the widgets concise
  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  // Static delegate for the localization
  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// Load the language JSON file from the "assets/l10n" folder
  Future<bool> load() async {
    try {
       log('Loading localization for ${locale.languageCode}');

      // Load the language JSON file from the "assets/l10n" folder
      String jsonString = await rootBundle.loadString(
          'assets/l10n/${locale.languageCode}-${locale.countryCode}.json');

      Map<String, dynamic> jsonMap = json.decode(jsonString);
      _localizedStrings = jsonMap;

      // Set the global instance
      AppLocalizations.instance = this;

      log('Localization loaded successfully');
      return true;
    } catch (e) {
      log('Error loading localization: $e');

      // If the requested locale is not available, load the default (en-US)
      if (locale.languageCode != 'en' || locale.countryCode != 'US') {
        try {
          String jsonString =
              await rootBundle.loadString('assets/l10n/en-US.json');
          Map<String, dynamic> jsonMap = json.decode(jsonString);
          _localizedStrings = jsonMap;
          
          // Set the global instance
          AppLocalizations.instance = this;
          
          log('Fallback to en-US localization');
          return true;
        } catch (e) {
          log('Error loading fallback localization: $e');
          return false;
        }
      }

      return false;
    }
  }

  /// Get a localized string by key
  /// The key can be a nested path like "auth.login"
  String translate(String key, {Map<String, String>? args}) {
    try {
      // Split the key by dots to navigate through the nested JSON
      List<String> keys = key.split('.');
      dynamic value = _localizedStrings;

      // Navigate through the nested JSON
      for (String k in keys) {
        if (value is Map && value.containsKey(k)) {
          value = value[k];
        } else {
          // Key not found, return the key itself
          return key;
        }
      }

      // If the value is not a string, return the key
      if (value is! String) {
        return key;
      }

      // Replace arguments in the string if provided
      String result = value;
      if (args != null) {
        args.forEach((argKey, argValue) {
          result = result.replaceAll('{$argKey}', argValue);
        });
      }

      return result;
    } catch (e) {
      log('Error translating key $key: $e');
      return key;
    }
  }

  /// Static method to get localized string without BuildContext
  /// This can be used in utility classes like ApiClient
  static String getString(String key, {Map<String, String>? args}) {
    if (_instance != null) {
      return _instance!.translate(key, args: args);
    }
    // Return the key if no instance is available
    return key;
  }
}

/// LocalizationsDelegate implementation
class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    // Supporting all languages defined in LanguageProvider
    return ['en', 'es', 'fr', 'de', 'zh', 'ja', 'ko', 'ru', 'ar', 'hi', 'te']
        .contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    AppLocalizations localizations = AppLocalizations(locale);
    await localizations.load();
    return localizations;
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

/// Extension method for easier access to translations
extension AppLocalizationsExtension on BuildContext {
    get l10n => AppLocalizations.of(this);

  /// Shorthand for translating a string
  String tr(String key, {Map<String, String>? args}) {
    return AppLocalizations.of(this).translate(key, args: args);
  }
}

extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${substring(1).toLowerCase()}";
  }
}

extension TitleCaseExtension on String {
  String toTitleCase() {
    return trim()
        .split(RegExp(r'\s+')) // Handles multiple spaces
        .map((word) => word.isEmpty
            ? ''
            : word[0].toUpperCase() + word.substring(1).toLowerCase())
        .join(' ');
  }
}
