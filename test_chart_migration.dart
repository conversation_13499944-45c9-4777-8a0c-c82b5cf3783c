import 'package:flutter/material.dart';
import 'package:ui_controls_library/widgets/ui_builder/properties_pie_chart.dart';
import 'package:ui_controls_library/widgets/ui_builder/common/config.dart';
import 'package:ui_controls_library/widgets/ui_builder/flexible_widget_serializer.dart';
import 'dart:convert';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Chart Migration Test',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: ChartMigrationTestScreen(),
    );
  }
}

class ChartMigrationTestScreen extends StatefulWidget {
  @override
  _ChartMigrationTestScreenState createState() => _ChartMigrationTestScreenState();
}

class _ChartMigrationTestScreenState extends State<ChartMigrationTestScreen> {
  ChartType selectedChartType = ChartType.ring;
  ChartSizeType selectedSize = ChartSizeType.medium;
  Widget? deserializedChart;

  void _testSerialization() {
    // Create the chart widget
    final chartWidget = RingPieChartUIBuilder(
      config: ChartSizeConfig(
        size: selectedSize,
        headingFontSize: 18,
        bodyFontSize: 16,
        labelFontSize: 14,
        chartRadius: 100,
        propertyType: "Properties",
        borderThikness: 2.0,
        borderRadius: 24,
        elevation: 2.0,
        chartType: selectedChartType,
      ),
      dataMap: {
        'Food': 20,
        'Rent': 15,
        'Transport': 10,
        'Savings': 12,
        'Others': 8,
        'Utilities': 10,
        'Insurance': 15,
        'Entertainment': 10,
      },
      colorList: [
        const Color(0xFF0D47A1),
        const Color(0xFF1565C0),
        const Color(0xFF1976D2),
        const Color(0xFF1E88E5),
        const Color(0xFF42A5F5),
        const Color(0xFF64B5F6),
        const Color(0xFF90CAF9),
        const Color(0xFFBBDEFB),
      ],
      onPressed: () {
        print('Chart button pressed');
      },
    );

    // Serialize the chart
    try {
      final serialized = FlexibleWidgetSerializer.serialize(chartWidget);
      print('Serialization successful: ${jsonEncode(serialized)}');

      // Deserialize the chart
      final deserialized = FlexibleWidgetSerializer.deserialize(serialized);

      setState(() {
        deserializedChart = deserialized;
      });

      print('Deserialization successful');
    } catch (e) {
      print('Serialization/Deserialization error: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Chart Migration Test'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Chart type selector
            Row(
              children: [
                Text('Chart Type: '),
                DropdownButton<ChartType>(
                  value: selectedChartType,
                  onChanged: (ChartType? newValue) {
                    setState(() {
                      selectedChartType = newValue!;
                    });
                  },
                  items: [
                    DropdownMenuItem(value: ChartType.ring, child: Text('Donut Chart')),
                    DropdownMenuItem(value: ChartType.disc, child: Text('Pie Chart')),
                    DropdownMenuItem(value: ChartType.bubble, child: Text('Bubble Chart')),
                    DropdownMenuItem(value: ChartType.scatter, child: Text('Scatter Chart')),
                    DropdownMenuItem(value: ChartType.bar, child: Text('Bar Chart')),
                  ],
                ),
                SizedBox(width: 20),
                Text('Size: '),
                DropdownButton<ChartSizeType>(
                  value: selectedSize,
                  onChanged: (ChartSizeType? newValue) {
                    setState(() {
                      selectedSize = newValue!;
                    });
                  },
                  items: [
                    DropdownMenuItem(value: ChartSizeType.small, child: Text('Small')),
                    DropdownMenuItem(value: ChartSizeType.medium, child: Text('Medium')),
                    DropdownMenuItem(value: ChartSizeType.large, child: Text('Large')),
                  ],
                ),
              ],
            ),
            SizedBox(height: 20),
            // Test serialization button
            ElevatedButton(
              onPressed: _testSerialization,
              child: Text('Test Serialization/Deserialization'),
            ),
            SizedBox(height: 20),
            // Chart display
            Expanded(
              child: Row(
                children: [
                  // Original chart
                  Expanded(
                    child: Column(
                      children: [
                        Text('Original Chart', style: TextStyle(fontWeight: FontWeight.bold)),
                        SizedBox(height: 10),
                        Expanded(
                          child: RingPieChartUIBuilder(
                            config: ChartSizeConfig(
                              size: selectedSize,
                              headingFontSize: 18,
                              bodyFontSize: 16,
                              labelFontSize: 14,
                              chartRadius: 100,
                              propertyType: "Properties",
                              borderThikness: 2.0,
                              borderRadius: 24,
                              elevation: 2.0,
                              chartType: selectedChartType,
                            ),
                            dataMap: {
                              'Food': 20,
                              'Rent': 15,
                              'Transport': 10,
                              'Savings': 12,
                              'Others': 8,
                              'Utilities': 10,
                              'Insurance': 15,
                              'Entertainment': 10,
                            },
                            colorList: [
                              const Color(0xFF0D47A1),
                              const Color(0xFF1565C0),
                              const Color(0xFF1976D2),
                              const Color(0xFF1E88E5),
                              const Color(0xFF42A5F5),
                              const Color(0xFF64B5F6),
                              const Color(0xFF90CAF9),
                              const Color(0xFFBBDEFB),
                            ],
                            onPressed: () {
                              print('Chart button pressed');
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: 20),
                  // Deserialized chart
                  Expanded(
                    child: Column(
                      children: [
                        Text('Deserialized Chart', style: TextStyle(fontWeight: FontWeight.bold)),
                        SizedBox(height: 10),
                        Expanded(
                          child: deserializedChart ?? Container(
                            child: Center(
                              child: Text('Click "Test Serialization" to see deserialized chart'),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
