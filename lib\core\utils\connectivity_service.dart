import 'package:connectivity_plus/connectivity_plus.dart';
import 'global_snackbar.dart';

class ConnectivityService {
  static final Connectivity _connectivity = Connectivity();
  static bool _wasOffline = false;

  /// Returns true if the device is connected to a network (WiFi or mobile)
  static Future<bool> isConnected() async {
    final result = await _connectivity.checkConnectivity();
    return result == ConnectivityResult.mobile || result == ConnectivityResult.wifi;
  }

  /// Optionally, listen to connectivity changes
  static Stream<ConnectivityResult> get onConnectivityChanged => _connectivity.onConnectivityChanged;

  /// Call this once to start listening for connection restoration
  static void listenForRestoration() {
    onConnectivityChanged.listen((result) async {
      final connected = result == ConnectivityResult.mobile || result == ConnectivityResult.wifi;
      if (connected && _wasOffline) {
        GlobalSnackbar.show('Internet connection restored');
      }
      _wasOffline = !connected ? true : false;
    });
  }
}
