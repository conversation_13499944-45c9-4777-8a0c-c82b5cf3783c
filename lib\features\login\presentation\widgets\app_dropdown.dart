
import 'package:flutter/material.dart';

class AppDropdown extends StatelessWidget {
  final List<String> items;
  final String? value;
  final String hint;
  final bool isLoading;
  final bool isError;
  final ValueChanged<String?>? onChanged;
  final String? errorText;

  const AppDropdown({
    super.key,
    required this.items,
    required this.value,
    required this.hint,
    this.isLoading = false,
    this.isError = false,
    this.onChanged,
    this.errorText,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (isError) {
      return Text(errorText ?? 'Failed to load');
    }
    return DropdownButton<String>(
      isExpanded: true,
      value: items.contains(value) ? value : null,
      hint: Text(hint),
      items: items.map((e) => DropdownMenuItem(value: e, child: Text(e))).toList(),
      onChanged: onChanged,
    );
  }
} 