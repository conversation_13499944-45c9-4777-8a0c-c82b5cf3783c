import 'dart:developer';

import 'package:builder_app/features/login/data/models/user_model.dart';
import 'package:builder_app/core/utils/api_client.dart';

import '../repositories/login_repository.dart';

class LoginUser {
  final LoginRepository repository;
  LoginUser(this.repository);

  Future<UserModel> call(LoginParams params) async {
    final userModel = await repository.login(email: params.email, password: params.password);
    final token = userModel.accessToken;
    if (token != null && token.isNotEmpty) {
      log('token: $token');
      ApiClient().setHeader('Authorization', 'Bearer $token');
    }
    return userModel;
  }
}

class LoginParams {
  final String email;
  final String password;

  LoginParams({required this.email, required this.password});
} 