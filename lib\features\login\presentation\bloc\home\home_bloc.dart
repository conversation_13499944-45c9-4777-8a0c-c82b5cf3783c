import 'package:flutter_bloc/flutter_bloc.dart';
import 'home_event.dart';
import 'home_state.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  HomeBloc() : super(const HomeState()) {
    on<FetchDropdownA>((event, emit) async {
      emit(state.copyWith(isLoadingA: true, errorA: null));
      try {
        await Future.delayed(const Duration(seconds: 3)); // Simulate API
        final items = ['Option1', 'Option2', 'Optio3','Option4'];
        // Keep previous selected value if still present
        String selected = state.selectedValueA;
        if (!items.contains(selected)) {
          selected = '';
        }
        emit(state.copyWith(dropdownItemsA: items, isLoadingA: false, errorA: null, selectedValueA: selected));
      } catch (e) {
        emit(state.copyWith(isLoadingA: false, errorA: 'Failed to load Dropdown A'));
      }
    });

    on<UpdateSelectedValueA>((event, emit) {
      emit(state.copyWith(selectedValueA: event.value));
    });

    on<DropdownAError>((event, emit) {
      emit(state.copyWith(isLoadingA: false, errorA: event.message));
    });
  }
}
