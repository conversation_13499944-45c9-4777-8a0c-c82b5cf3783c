
class HomeState  {
  final List<String> dropdownItemsA;
  final String selectedValueA;
  final bool isLoadingA;
  final String? errorA;

  const HomeState({
    this.dropdownItemsA = const [],
    this.selectedValueA = '',
    this.isLoadingA = false,
    this.errorA,
  });

  HomeState copyWith({
    List<String>? dropdownItemsA,
    String? selectedValueA,
    bool? isLoadingA,
    String? errorA,
  }) {
    return HomeState(
      dropdownItemsA: dropdownItemsA ?? this.dropdownItemsA,
      selectedValueA: selectedValueA ?? this.selectedValueA,
      isLoadingA: isLoadingA ?? this.isLoadingA,
      errorA: errorA,
    );
  }

}
