import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:graphic/graphic.dart' as graphic;
import 'package:fl_chart/fl_chart.dart';
import 'package:ui_controls_library/widgets/ui_builder/flexible_widget_serializer.dart';

import 'common/config.dart';
import 'common/size_responsive_config.dart';

class RingPieChartUIBuilder extends StatefulWidget {
  final Function onPressed;
  final ChartSizeConfig config;
  final Map<String, double>? dataMap;
  final List<Color>? colorList;

  const RingPieChartUIBuilder({
    super.key,
    required this.onPressed,
    required this.config,
    this.dataMap,
    this.colorList,
  });

  @override
  State<RingPieChartUIBuilder> createState() => RingPieChartUIBuilderState();
}

class RingPieChartUIBuilderState extends State<RingPieChartUIBuilder> {
  late Widget screenWidgetTree;
  String jsonOutput = '';
  Widget? deserializedWidget;
  bool _isGenerateUIEnabled = false; // UI state management for Generate UI button

  Map<String, double> get effectiveDataMap =>
      widget.dataMap ??
      {
        'Point 1': 1,
        'Point 2': 1,
        'Point 3': 1,
        'Point 4': 1,
        'Point 5': 1,
        'Point 6': 1,
        'Point 7': 1,
        'Point 8': 1,
      };

  List<Color> get effectiveColorList =>
      widget.colorList ??
      [
        const Color(0xFF0D47A1), // Dark Blue for Food
        const Color(0xFF1565C0), // Medium Dark Blue for Rent
        const Color(0xFF1976D2), // Blue for Transport
        const Color(0xFF1E88E5), // Medium Blue for Savings
        const Color(0xFF2196F3), // Light Blue for Others
        const Color(0xFF42A5F5), // Lighter Blue for Utilities
        const Color(0xFF64B5F6), // Very Light Blue for Insurance
        const Color(0xFF90CAF9), // Lightest Blue for Entertainment
      ];

  Widget buildChartSection({required ChartSizeConfig c}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          c.size?.name.toUpperCase() ?? "",
          style: const TextStyle(fontSize: 24),
        ),
        Row(
          children: [
            Expanded(
              flex: 10,
              child: Card(
                elevation: c.elevation,
                // shadowColor: Colors.black,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(c.borderRadius),
                  side: BorderSide(
                    color: const Color.fromARGB(255, 237, 235, 235),
                    width: c.borderThikness ?? 0,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Temperature theme",
                        style: TextStyle(
                          fontSize: c.bodyFontSize,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      // Show different layouts based on chart type
                      c.chartType == ChartType.bubble ||
                              c.chartType == ChartType.scatter ||
                              c.chartType == ChartType.bar
                          ? _buildChart(
                            c,
                          ) // Bubble, scatter, and bar charts have their own layout
                          : Row(
                            children: [
                              Expanded(child: _buildChart(c)),
                              // SfCircularChart(
                              //           title: ChartTitle(text: 'Sales by Category'),
                              //           legend: const Legend(
                              //             isVisible: true,
                              //             overflowMode: LegendItemOverflowMode.wrap,
                              //           ),
                              //           tooltipBehavior: TooltipBehavior(enable: true),
                              //           series: <PieSeries<_ChartData, String>>[
                              //             PieSeries<_ChartData, String>(
                              //               radius: "100%",
                              //               dataSource: _getChartData(),
                              //               xValueMapper: (_ChartData data, _) => data.category,
                              //               yValueMapper: (_ChartData data, _) => data.value,
                              //               dataLabelMapper: (_ChartData data, _) => '${data.category}: ${data.value}',
                              //               dataLabelSettings: const DataLabelSettings(isVisible: true),
                              //             )
                              //           ],
                              //         ),

                              //   child: PieChart(
                              //   dataMap: dataMap,
                              //   colorList: colorList,
                              //   chartType: c.chartType,
                              //   chartRadius: c.chartRadius ?? 80,
                              //   ringStrokeWidth: (c.chartRadius ?? 80) * 0.2,
                              //   centerWidget:c.chartType==ChartType.ring? Text(
                              //     "Total Value\n\$9,999.99",
                              //     textAlign: TextAlign.center,
                              //     style: TextStyle(
                              //       fontWeight: FontWeight.bold,
                              //       fontSize: (c.chartRadius ?? 80) / 8,
                              //     ),
                              //   ):null,
                              //   legendOptions: const LegendOptions(showLegends: false),
                              //   chartValuesOptions: const ChartValuesOptions(showChartValues: false),
                              // ),
                              Expanded(
                                child: buildLegend(c.labelFontSize ?? 0),
                              ),
                            ],
                          ),
                    ],
                  ),
                ),
              ),
            ),
            const Expanded(
              // flex: 15,
              child: SizedBox(width: 20),
            ),
          ],
        ),
        const SizedBox(height: 24),
        Text(
          "Properties",
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: c.bodyFontSize,
            color: Colors.black,
          ),
        ),
        Text(
          "Heading 1 Medium ${c.headingFontSize?.toInt()}",
          style: TextStyle(
            fontSize: c.headingFontSize,
            fontWeight: FontWeight.w500,
          ),
        ),
        Text(
          "Body 1 Regular ${c.bodyFontSize?.toInt()}",
          style: TextStyle(fontSize: c.bodyFontSize),
        ),
        Text(
          "Label - Regular ${c.labelFontSize?.toInt()}",
          style: TextStyle(fontSize: c.labelFontSize, color: Colors.grey[700]),
        ),
      ],
    );
  }

  Widget _buildChart(ChartSizeConfig c) {
    switch (c.chartType) {
      case ChartType.ring:
        // Build Donut Chart using Graphic package
        return _buildGraphicDonutChart(c, effectiveColorList);

      case ChartType.disc:
        // Build Pie Chart using Graphic package
        return _buildGraphicPieChart(c, effectiveColorList);

      case ChartType.bubble:
        // Build Bubble Chart using Graphic package
        return _buildGraphicBubbleChart(c, effectiveColorList);

      case ChartType.scatter:
        // Build Scatter Plot Chart using Graphic package
        return _buildGraphicScatterChart(c, effectiveColorList);

      case ChartType.bar:
        // Build Bar Chart using Graphic package
        return _buildGraphicBarChart(c, effectiveColorList);

      case ChartType.histogram:
        // Build Histogram Chart using FL Chart package
        return _buildFlHistogramChart(c, effectiveColorList);
    }
  }



  Widget buildLegend(double fontSize) {
    final numRows = (effectiveDataMap.length / 2).ceil();

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: List.generate(numRows, (rowIndex) {
        final int item1Index = rowIndex;
        final double item2IndexDouble = rowIndex + numRows.toDouble();
        final int item2Index = item2IndexDouble.toInt();
        final labels = effectiveDataMap.keys.toList();

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 2.0),
          child: Row(
            children: [
              Expanded(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      color: effectiveColorList[item1Index],
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        labels[item1Index],
                        style: TextStyle(fontSize: fontSize),
                        overflow: TextOverflow.visible,
                        softWrap: true,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child:
                    item2Index < labels.length
                        ? Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              width: 12,
                              height: 12,
                              color: effectiveColorList[item2Index],
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                labels[item2Index],
                                style: TextStyle(fontSize: fontSize),
                                overflow: TextOverflow.visible,
                                softWrap: true,
                              ),
                            ),
                          ],
                        )
                        : const SizedBox(), // Placeholder for alignment if odd number of items
              ),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildBubbleLegend(double fontSize) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        // Group header
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.blue.shade100,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            'Group1',
            style: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.w500,
              color: Colors.blue.shade700,
            ),
          ),
        ),
        const SizedBox(height: 12),

        // Legend items
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildBubbleLegendItem(
              color: Colors.blue.shade600,
              label: 'X-label - 50',
              fontSize: fontSize,
            ),
            const SizedBox(height: 4),
            _buildBubbleLegendItem(
              color: Colors.blue.shade500,
              label: 'Y-label - 50',
              fontSize: fontSize,
            ),
            const SizedBox(height: 4),
            _buildBubbleLegendItem(
              color: Colors.blue.shade400,
              label: 'Value 3 - 100',
              fontSize: fontSize,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBubbleLegendItem({
    required Color color,
    required String label,
    required double fontSize,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(color: color, shape: BoxShape.rectangle),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            label,
            style: TextStyle(fontSize: fontSize, color: Colors.black87),
          ),
        ),
      ],
    );
  }

  void _rebuildWidgetTree() {
    final c = widget.config;
    screenWidgetTree = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          c.propertyType ?? "",
          style: const TextStyle(fontSize: 33, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 32),
        buildChartSection(c: c),
      ],
    );
  }

  void _resetUIState() {
    setState(() {
      _isGenerateUIEnabled = false;
      jsonOutput = '';
      deserializedWidget = null;
    });
  }

  @override
  void initState() {
    super.initState();
    _rebuildWidgetTree();
  }

  @override
  void didUpdateWidget(covariant RingPieChartUIBuilder oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.config != widget.config) {
      _resetUIState(); // Reset UI state when config changes
      setState(() => _rebuildWidgetTree());
    }
  }

  Future<void> generateBuildUI() async {
    try {
      // Generate JSON from current widget tree
      final generated = FlexibleWidgetSerializer.serialize(screenWidgetTree);
      final jsonString = const JsonEncoder.withIndent('  ').convert(generated);

      print("=== Ring Pie Chart Serialized Widget JSON ===\n$jsonString");

      // Deserialize JSON back to widget
      final rebuiltWidget = FlexibleWidgetSerializer.deserialize(generated);

      setState(() {
        deserializedWidget = rebuiltWidget;
      });

      print("=== JSON to Widget Conversion Complete ===");
    } catch (e) {
      print("Error in JSON generation/deserialization: $e");
      setState(() {
        jsonOutput = 'Error: $e';
        deserializedWidget = null;
      });
    }
  }

  Future<void> generateJson() async {
    try {
      // Generate JSON from current widget tree
      final generated = FlexibleWidgetSerializer.serialize(screenWidgetTree);
      final jsonString = const JsonEncoder.withIndent('  ').convert(generated);

      setState(() {
        jsonOutput = jsonString;
        _isGenerateUIEnabled = true; // Enable the Generate UI button
      });

      print("=== JSON to Widget Conversion Complete ===");
    } catch (e) {
      print("Error in JSON generation/deserialization: $e");
      setState(() {
        jsonOutput = 'Error: $e';
        deserializedWidget = null;
        _isGenerateUIEnabled = false; // Disable on error
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Generate JSON Button
          Wrap(
            spacing: 12,
            runSpacing: 10,
            children: [
              OutlinedButton.icon(
                onPressed: generateJson,
                icon: const Icon(Icons.code, size: 20),
                label: const Text('Generate JSON'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.black87,
                  side: const BorderSide(color: Colors.grey),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12,
                  ),
                  textStyle: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              OutlinedButton.icon(
                onPressed: _isGenerateUIEnabled ? generateBuildUI : null,
                icon: const Icon(Icons.auto_fix_high_outlined, size: 20),
                label: const Text('Generate UI from JSON'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: _isGenerateUIEnabled ? Colors.black87 : Colors.grey,
                  side: BorderSide(color: _isGenerateUIEnabled ? Colors.grey : Colors.grey.shade300),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12,
                  ),
                  textStyle: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              OutlinedButton.icon(
                onPressed: () {
                  setState(() {
                    jsonOutput = '';
                    deserializedWidget = null;
                    _isGenerateUIEnabled = false; // Reset UI state
                  });
                },
                icon: const Icon(Icons.clear, size: 20),
                label: const Text('Clear'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.black87,
                  side: const BorderSide(color: Colors.grey),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12,
                  ),
                  textStyle: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Horizontal scrollable row with widgets
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Original Widget
              Container(
                width: 450,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: screenWidgetTree,
              ),

              // JSON Output
              if (jsonOutput.isNotEmpty) ...[
                Expanded(
                  child: Container(
                    height: 400,
                    margin: const EdgeInsets.all(8),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.blue.shade200),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: SelectableText(
                      jsonOutput.isNotEmpty ? jsonOutput : 'No JSON generated',
                      style: const TextStyle(
                        fontSize: 12,
                        fontFamily: 'Courier',
                      ),
                    ),
                  ),
                ),
              ],

              // Deserialized Widget
              if (deserializedWidget != null)
                Container(
                  width: 450,
                  margin: const EdgeInsets.all(8),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.green.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: deserializedWidget!,
                ),
            ],
          ),
        ],
      ),
    );
  }

  List<_ChartData> _getChartData() {
    return [
      _ChartData('Food', 20),
      _ChartData('Rent', 15),
      _ChartData('Transport', 10),
      _ChartData('Savings', 12),
      _ChartData('Others', 8),
      _ChartData('Utilities', 10),
      _ChartData('Insurance', 15),
      _ChartData('Entertainment', 10),
    ];
  }

  List<_BubbleChartData> _getBubbleChartData() {
    return [
      _BubbleChartData(10, 20, 15),
      _BubbleChartData(20, 30, 25),
      _BubbleChartData(30, 15, 20),
      _BubbleChartData(40, 35, 30),
      _BubbleChartData(50, 25, 18),
      _BubbleChartData(60, 40, 35),
      _BubbleChartData(70, 30, 22),
      _BubbleChartData(80, 45, 28),
    ];
  }

  // Build Graphic package scatter chart
  Widget _buildGraphicScatterChart(ChartSizeConfig c, List<Color> colorList) {
    final scatterData = _getScatterChartData();

    // Convert scatter data to Map format for graphic package
    final data =
        scatterData
            .asMap()
            .entries
            .map(
              (entry) => {
                'x': entry.value.x,
                'y': entry.value.y,
                'index': entry.key,
              },
            )
            .toList();

    return Container(
      height: 300, // Fixed height for consistency
      child: graphic.Chart(
        data: data,
        variables: {
          'x': graphic.Variable(
            accessor: (Map map) => map['x'] as num,
            scale: graphic.LinearScale(min: 0, max: 35, tickCount: 8),
          ),
          'y': graphic.Variable(
            accessor: (Map map) => map['y'] as num,
            scale: graphic.LinearScale(min: 2000, max: 8000, tickCount: 7),
          ),
          'index': graphic.Variable(accessor: (Map map) => map['index'] as num),
        },
        marks: [
          graphic.PointMark(
            position: graphic.Varset('x') * graphic.Varset('y'),
            size: graphic.SizeEncode(value: _getScatterPointSize(c.size)),
            color: graphic.ColorEncode(variable: 'index', values: colorList),
            shape: graphic.ShapeEncode(value: graphic.CircleShape()),
          ),
        ],
        axes: [graphic.Defaults.horizontalAxis, graphic.Defaults.verticalAxis],
        coord: graphic.RectCoord(),
      ),
    );
  }

  // Build Graphic package bar chart
  Widget _buildGraphicBarChart(ChartSizeConfig c, List<Color> colorList) {
    final barData = _getBarChartData();

    // Convert bar data to Map format for graphic package
    final data =
        barData
            .asMap()
            .entries
            .map(
              (entry) => {
                'category': entry.value.category,
                'value': entry.value.value,
                'index': entry.key,
              },
            )
            .toList();

    // Size-responsive properties
    double chartHeight = _getBarChartHeight(c.size);
    double barWidth = _getBarWidth(c.size);

    return Container(
      height: chartHeight,
      child: graphic.Chart(
        data: data,
        variables: {
          'category': graphic.Variable(
            accessor: (dynamic map) => (map as Map)['category'] as String,
          ),
          'value': graphic.Variable(
            accessor: (dynamic map) => (map as Map)['value'] as num,
            scale: graphic.LinearScale(min: 0, max: 120, tickCount: 7),
          ),
          'index': graphic.Variable(
            accessor: (dynamic map) => (map as Map)['index'] as num,
          ),
        },
        marks: [
          graphic.IntervalMark(
            position: graphic.Varset('category') * graphic.Varset('value'),
            color: graphic.ColorEncode(variable: 'index', values: colorList),
            size: graphic.SizeEncode(value: barWidth),
          ),
        ],
        axes: [graphic.Defaults.horizontalAxis, graphic.Defaults.verticalAxis],
        coord: graphic.RectCoord(),
      ),
    );
  }

  // Build FL Chart histogram chart
  Widget _buildFlHistogramChart(ChartSizeConfig c, List<Color> colorList) {
    final histogramData = _getHistogramChartData();

    // Size-responsive properties
    double chartHeight = _getHistogramChartHeight(c.size);
    double barWidth = _getHistogramBarWidth(c.size);
    double fontSize = _getHistogramFontSize(c.size);

    // Create bar chart data for fl_chart
    final barGroups = histogramData.asMap().entries.map((entry) {
      final index = entry.key;
      final data = entry.value;

      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: data.frequency,
            color: colorList.isNotEmpty ? colorList[index % colorList.length] : Colors.blue,
            width: barWidth,
            borderRadius: BorderRadius.circular(0),
          ),
        ],
      );
    }).toList();

    return Container(
      height: chartHeight,
      child: Column(
        children: [
          // Title
          Text(
            'Temperature theme',
            style: TextStyle(
              fontSize: c.headingFontSize ?? 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          // Chart
          Expanded(
            child: BarChart(
              BarChartData(
                alignment: BarChartAlignment.spaceEvenly,
                maxY: histogramData.map((e) => e.frequency).reduce((a, b) => a > b ? a : b) + 1,
                barTouchData: BarTouchData(enabled: false),
                titlesData: FlTitlesData(
                  show: true,
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (double value, TitleMeta meta) {
                        final index = value.toInt();
                        if (index >= 0 && index < histogramData.length) {
                          return Text(
                            histogramData[index].binStart.toInt().toString(),
                            style: TextStyle(fontSize: fontSize, color: Colors.black87),
                          );
                        }
                        return const Text('');
                      },
                      reservedSize: 30,
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (double value, TitleMeta meta) {
                        return Text(
                          value.toInt().toString(),
                          style: TextStyle(fontSize: fontSize, color: Colors.black87),
                        );
                      },
                      reservedSize: 30,
                    ),
                  ),
                  topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                ),
                gridData: FlGridData(
                  show: true,
                  drawVerticalLine: false,
                  horizontalInterval: 1,
                  getDrawingHorizontalLine: (value) {
                    return FlLine(
                      color: Colors.grey.withValues(alpha: 0.3),
                      strokeWidth: 0.5,
                    );
                  },
                ),
                borderData: FlBorderData(
                  show: true,
                  border: Border(
                    left: BorderSide(color: Colors.black87, width: 1),
                    bottom: BorderSide(color: Colors.black87, width: 1),
                  ),
                ),
                barGroups: barGroups,
              ),
            ),
          ),
          // X-axis label
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Text(
              'Value',
              style: TextStyle(
                fontSize: c.labelFontSize ?? 12,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build Graphic package pie chart
  Widget _buildGraphicPieChart(ChartSizeConfig c, List<Color> colorList) {
    final pieData = _getChartData();

    // Convert pie data to Map format for graphic package
    final data = pieData
        .asMap()
        .entries
        .map(
          (entry) => {
            'category': entry.value.category,
            'value': entry.value.value,
            'index': entry.key,
          },
        )
        .toList();

    // Size-responsive properties
    double chartSize = _getPieChartSize(c.size);

    return Container(
      width: chartSize,
      height: chartSize,
      child: graphic.Chart(
        data: data,
        variables: {
          'category': graphic.Variable(
            accessor: (dynamic map) => (map as Map)['category'] as String,
          ),
          'value': graphic.Variable(
            accessor: (dynamic map) => (map as Map)['value'] as num,
            scale: graphic.LinearScale(min: 0),
          ),
          'index': graphic.Variable(
            accessor: (dynamic map) => (map as Map)['index'] as num,
          ),
        },
        marks: [
          graphic.IntervalMark(
            position: graphic.Varset('value'),
            color: graphic.ColorEncode(variable: 'index', values: colorList),
            size: graphic.SizeEncode(value: 1.0), // Full pie chart - no inner hole
          ),
        ],
        coord: graphic.PolarCoord(
          transposed: true,
          dimCount: 1,
        ),
      ),
    );
  }

  // Build Graphic package donut chart
  Widget _buildGraphicDonutChart(ChartSizeConfig c, List<Color> colorList) {
    final donutData = _getChartData();

    // Convert donut data to Map format for graphic package
    final data = donutData
        .asMap()
        .entries
        .map(
          (entry) => {
            'category': entry.value.category,
            'value': entry.value.value,
            'index': entry.key,
          },
        )
        .toList();

    // Size-responsive properties
    double chartSize = _getDonutChartSize(c.size);
    double totalValue = donutData.fold(0, (sum, item) => sum + item.value);

    return Container(
      width: chartSize,
      height: chartSize,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Main donut chart using graphic package
          graphic.Chart(
            data: data,
            variables: {
              'category': graphic.Variable(
                accessor: (dynamic map) => (map as Map)['category'] as String,
              ),
              'value': graphic.Variable(
                accessor: (dynamic map) => (map as Map)['value'] as num,
                scale: graphic.LinearScale(min: 0),
              ),
              'index': graphic.Variable(
                accessor: (dynamic map) => (map as Map)['index'] as num,
              ),
            },
            marks: [
              graphic.IntervalMark(
                position: graphic.Varset('value'),
                color: graphic.ColorEncode(variable: 'index', values: colorList),
                size: graphic.SizeEncode(value: 0.5), // Better donut hole ratio for cleaner appearance
              ),
            ],
            coord: graphic.PolarCoord(
              transposed: true,
              dimCount: 1,
            ),
          ),
          // Center text overlay with constant size across all chart sizes
          Container(
            width: 100.0, // Fixed size for all chart sizes
            height: 100.0, // Fixed size for all chart sizes
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "Total Value",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 8.0, // Fixed font size
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    "\$9,999.99",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 12.0, // Fixed font size
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build Graphic package bubble chart
  Widget _buildGraphicBubbleChart(ChartSizeConfig c, List<Color> colorList) {
    final bubbleData = _getBubbleChartData();

    // Convert bubble data to Map format for graphic package
    final data = bubbleData
        .asMap()
        .entries
        .map(
          (entry) => {
            'x': entry.value.x,
            'y': entry.value.y,
            'size': entry.value.size,
            'index': entry.key,
          },
        )
        .toList();

    // Size-responsive properties
    double chartHeight = _getBubbleChartHeight(c.size);

    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Container(
            height: chartHeight,
            child: graphic.Chart(
              data: data,
              variables: {
                'x': graphic.Variable(
                  accessor: (dynamic map) => (map as Map)['x'] as num,
                  scale: graphic.LinearScale(min: 0, max: 100, tickCount: 6),
                ),
                'y': graphic.Variable(
                  accessor: (dynamic map) => (map as Map)['y'] as num,
                  scale: graphic.LinearScale(min: 0, max: 50, tickCount: 6),
                ),
                'size': graphic.Variable(
                  accessor: (dynamic map) => (map as Map)['size'] as num,
                ),
                'index': graphic.Variable(
                  accessor: (dynamic map) => (map as Map)['index'] as num,
                ),
              },
              marks: [
                graphic.PointMark(
                  position: graphic.Varset('x') * graphic.Varset('y'),
                  size: graphic.SizeEncode(
                    variable: 'size',
                    values: [5, 10, 15, 20, 25, 30, 35, 40], // Size range for bubbles
                  ),
                  color: graphic.ColorEncode(variable: 'index', values: colorList),
                  shape: graphic.ShapeEncode(value: graphic.CircleShape()),
                ),
              ],
              axes: [graphic.Defaults.horizontalAxis, graphic.Defaults.verticalAxis],
              coord: graphic.RectCoord(),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(flex: 1, child: _buildBubbleLegend(c.labelFontSize ?? 10)),
      ],
    );
  }

  double _getScatterPointSize(ChartSizeType? size) {
    if (size == null) return 8.0;
    final config = SizeResponsiveConfig.getChartConfig(ChartType.scatter, size);
    return config.pointSize ?? 8.0;
  }

  // Helper methods for bar chart size-responsive properties
  double _getBarChartHeight(ChartSizeType? size) {
    if (size == null) return 280.0;
    final config = SizeResponsiveConfig.getChartConfig(ChartType.bar, size);
    return config.chartHeight ?? 280.0;
  }

  double _getBarWidth(ChartSizeType? size) {
    if (size == null) return 25.0;
    final config = SizeResponsiveConfig.getChartConfig(ChartType.bar, size);
    return config.barWidth ?? 25.0;
  }

  // Helper methods for histogram chart size-responsive properties
  double _getHistogramChartHeight(ChartSizeType? size) {
    if (size == null) return 280.0;
    final config = SizeResponsiveConfig.getChartConfig(ChartType.histogram, size);
    return config.chartHeight ?? 280.0;
  }

  double _getHistogramBarWidth(ChartSizeType? size) {
    if (size == null) return 22.0;
    final config = SizeResponsiveConfig.getChartConfig(ChartType.histogram, size);
    return config.barWidth ?? 22.0;
  }

  double _getHistogramFontSize(ChartSizeType? size) {
    if (size == null) return 10.0;
    final config = SizeResponsiveConfig.getChartConfig(ChartType.histogram, size);
    return config.axisLabelFontSize ?? 10.0;
  }

  // Helper methods for pie chart size-responsive properties
  double _getPieChartHeight(ChartSizeType? size) {
    switch (size) {
      case ChartSizeType.small:
        return 250.0;
      case ChartSizeType.medium:
        return 320.0;
      case ChartSizeType.large:
        return 400.0;
      default:
        return 320.0;
    }
  }

  double _getPieChartSize(ChartSizeType? size) {
    switch (size) {
      case ChartSizeType.small:
        return 250.0;
      case ChartSizeType.medium:
        return 320.0;
      case ChartSizeType.large:
        return 400.0;
      default:
        return 320.0;
    }
  }

  // Helper methods for donut chart size-responsive properties
  double _getDonutChartHeight(ChartSizeType? size) {
    switch (size) {
      case ChartSizeType.small:
        return 250.0;
      case ChartSizeType.medium:
        return 320.0;
      case ChartSizeType.large:
        return 400.0;
      default:
        return 320.0;
    }
  }

  double _getDonutChartSize(ChartSizeType? size) {
    switch (size) {
      case ChartSizeType.small:
        return 250.0;
      case ChartSizeType.medium:
        return 320.0;
      case ChartSizeType.large:
        return 400.0;
      default:
        return 320.0;
    }
  }

  double _getDonutInnerRadius(ChartSizeType? size) {
    switch (size) {
      case ChartSizeType.small:
        return 65.0;
      case ChartSizeType.medium:
        return 65.0;
      case ChartSizeType.large:
        return 65.0;
      default:
        return 65.0;
    }
  }

  double _getDonutCenterTextSize(ChartSizeType? size) {
    switch (size) {
      case ChartSizeType.small:
        return 12.0;
      case ChartSizeType.medium:
        return 14.0;
      case ChartSizeType.large:
        return 16.0;
      default:
        return 14.0;
    }
  }

  // Helper methods for bubble chart size-responsive properties
  double _getBubbleChartHeight(ChartSizeType? size) {
    switch (size) {
      case ChartSizeType.small:
        return 200.0;
      case ChartSizeType.medium:
        return 280.0;
      case ChartSizeType.large:
        return 350.0;
      default:
        return 280.0;
    }
  }

  List<_ScatterChartData> _getScatterChartData() {
    return [
      _ScatterChartData(5, 3000),
      _ScatterChartData(8, 3500),
      _ScatterChartData(12, 4000),
      _ScatterChartData(15, 4500),
      _ScatterChartData(18, 5000),
      _ScatterChartData(22, 5500),
      _ScatterChartData(25, 6000),
      _ScatterChartData(28, 6500),
      _ScatterChartData(32, 7000),
      _ScatterChartData(35, 7500),
    ];
  }

  List<_BarChartData> _getBarChartData() {
    return [
      _BarChartData('Jan', 30),
      _BarChartData('Feb', 45),
      _BarChartData('Mar', 60),
      _BarChartData('Apr', 75),
      _BarChartData('May', 90),
      _BarChartData('Jun', 85),
      _BarChartData('Jul', 100),
      _BarChartData('Aug', 95),
    ];
  }

  List<_HistogramChartData> _getHistogramChartData() {
    // Generate sample temperature data that matches the reference image
    return [
      _HistogramChartData(0, 5, 3, 0),
      _HistogramChartData(5, 10, 3, 1),
      _HistogramChartData(10, 15, 3, 2),
      _HistogramChartData(15, 20, 3, 3),
      _HistogramChartData(20, 25, 4, 4),
      _HistogramChartData(25, 30, 3, 5),
      _HistogramChartData(30, 35, 2, 6),
      _HistogramChartData(35, 40, 3, 7),
      _HistogramChartData(40, 45, 3, 8),
      _HistogramChartData(45, 50, 2, 9),
      _HistogramChartData(50, 55, 2, 10),
    ];
  }
}

class _ChartData {
  final String category;
  final double value;

  _ChartData(this.category, this.value);
}

class _BubbleChartData {
  final double x;
  final double y;
  final double size;

  _BubbleChartData(this.x, this.y, this.size);
}

class _ScatterChartData {
  final double x;
  final double y;

  _ScatterChartData(this.x, this.y);
}

class _BarChartData {
  final String category;
  final double value;

  _BarChartData(this.category, this.value);
}

class _HistogramChartData {
  final double binStart;
  final double binEnd;
  final double frequency;
  final int binIndex;

  _HistogramChartData(this.binStart, this.binEnd, this.frequency, this.binIndex);

  double get binCenter => (binStart + binEnd) / 2;
  double get binWidth => binEnd - binStart;
}
