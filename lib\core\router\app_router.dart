import 'dart:async';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:builder_app/features/login/presentation/bloc/app_config/config_bloc.dart';
import 'package:builder_app/features/login/presentation/bloc/app_config/config_state.dart';
import 'package:builder_app/features/login/presentation/screens/login_screen.dart';
import 'package:builder_app/features/dashboard/home_screen.dart';

import '../../features/dashboard/widgets/typography_json_widget.dart';

class AppRouter {
  static GoRouter createRouter(AppConfigBloc bloc) {
    return GoRouter(
      initialLocation: '/',
      refreshListenable: GoRouterRefreshStream(bloc.stream),
      redirect: (context, state) {
        final blocState = bloc.state;
        final loggedIn = blocState is UserLoggedInState;
        final loggingIn = state.uri.toString() == '/login';
        log('GoRouter redirect: bloc.state=$blocState, uri=${state.uri}');
        // if (!loggedIn && !loggingIn) return '/login';
        // if (loggedIn && loggingIn) return '/home';
        return null;
      },
      routes: [
        GoRoute(
          path: '/',
          redirect: (_, __) => '/home',
        ),
        GoRoute(
          path: '/login',
          builder: (context, state) {
            final Map<String, dynamic>? args = state.extra as Map<String, dynamic>?;
            return LoginPage(
              redirectMessage: args?['redirectMessage'] as String?,
              email: args?['email'] as String?,
            );
          },
        ),
        GoRoute(
          path: '/home',
          builder: (context, state) {
            return HomeScreen();
          },
        ),
        GoRoute(path: '/json',
        builder: (context, state) {
          var data=state.extra as Map<String,dynamic>;
          return JsonWidgetScreen(jsonSource: data,);
        },)
     
      ],
    );
  }
}

class GoRouterRefreshStream extends ChangeNotifier {
  GoRouterRefreshStream(Stream<dynamic> stream) {
    notifyListener = () => notifyListeners();
    _subscription = stream.asBroadcastStream().listen((_) => notifyListener());
  }
  late final void Function() notifyListener;
  late final StreamSubscription<dynamic> _subscription;
  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }
} 