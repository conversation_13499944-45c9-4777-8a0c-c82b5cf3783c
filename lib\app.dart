
import 'package:builder_app/core/utils/app_theme.dart';
import 'package:builder_app/features/login/presentation/bloc/app_config/config_bloc.dart';
import 'package:builder_app/l10n/app_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:builder_app/core/utils/global_snackbar.dart';
import 'package:builder_app/core/utils/connectivity_service.dart';
import 'package:builder_app/core/router/app_router.dart';
import 'package:responsive_framework/responsive_framework.dart';
import 'package:go_router/go_router.dart';

class App extends StatefulWidget {
   const App({super.key,});

  @override
  State<App> createState() => _AppState();
}

class _AppState extends State<App> {
  late final AppConfigBloc _bloc;
  late final GoRouter _router;

  @override
  void initState() {
    super.initState();
    ConnectivityService.listenForRestoration();
    _bloc = AppConfigBloc()..checkLoginStatus();
    _router = AppRouter.createRouter(_bloc);
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _bloc,
      child: MaterialApp.router(
        debugShowCheckedModeBanner: false,
        title: 'Builder App',
        locale: _bloc.locale,
        theme: lightTheme,
        darkTheme: darkTheme,
        themeMode: ThemeMode.system,
        builder: (context, child) => ResponsiveBreakpoints.builder(
          child: child!,
          breakpoints: [
            const Breakpoint(start: 0, end: 450, name: MOBILE),
            const Breakpoint(start: 451, end: 800, name: TABLET),
            const Breakpoint(start: 801, end: 1920, name: DESKTOP),
            const Breakpoint(start: 1921, end: double.infinity, name: '4K'),
          ],
        ),
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [Locale('en', 'US')],
        routerConfig: _router,
        scaffoldMessengerKey: GlobalSnackbar.messengerKey,
        
      ),
    );
  }
}