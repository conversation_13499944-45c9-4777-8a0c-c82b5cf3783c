import 'package:flutter/material.dart';
import 'package:ui_controls_library/widgets/ui_builder/properties_pie_chart.dart';
import 'package:ui_controls_library/widgets/ui_builder/common/config.dart';
import 'package:ui_controls_library/widgets/ui_builder/flexible_widget_serializer.dart';
import 'dart:convert';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Bar Chart Test',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: BarChartTestScreen(),
    );
  }
}

class BarChartTestScreen extends StatefulWidget {
  @override
  _BarChartTestScreenState createState() => _BarChartTestScreenState();
}

class _BarChartTestScreenState extends State<BarChartTestScreen> {
  String jsonOutput = '';
  Widget? deserializedWidget;
  bool testPassed = false;
  String testResult = '';

  @override
  void initState() {
    super.initState();
    _runTest();
  }

  void _runTest() {
    try {
      // Test 1: Create bar chart widget
      final barChartConfig = ChartSizeConfig(
        size: ChartSizeType.medium,
        headingFontSize: 16,
        bodyFontSize: 14,
        labelFontSize: 12,
        chartRadius: 100,
        propertyType: "Properties",
        borderThikness: 1,
        borderRadius: 6,
        elevation: 0,
        chartType: ChartType.bar,
      );

      final colorList = [
        Colors.blue,
        Colors.green,
        Colors.orange,
        Colors.red,
        Colors.purple,
        Colors.teal,
        Colors.amber,
        Colors.pink,
      ];

      final barChartWidget = RingPieChartUIBuilder(
        config: barChartConfig,
        colorList: colorList,
        onPressed: () {
          print('Bar chart button pressed');
        },
      );

      // Test 2: Serialize to JSON
      final serialized = FlexibleWidgetSerializer.serialize(barChartWidget);
      final jsonString = const JsonEncoder.withIndent('  ').convert(serialized);
      
      // Test 3: Deserialize back to widget
      final rebuiltWidget = FlexibleWidgetSerializer.deserialize(serialized);

      setState(() {
        jsonOutput = jsonString;
        deserializedWidget = rebuiltWidget;
        testPassed = rebuiltWidget != null;
        testResult = testPassed 
          ? 'SUCCESS: Bar chart created, serialized, and deserialized successfully!'
          : 'FAILED: Could not deserialize bar chart widget';
      });

      print("=== BAR CHART TEST RESULTS ===");
      print("Test passed: $testPassed");
      print("JSON output length: ${jsonString.length}");
      print("Deserialized widget: ${rebuiltWidget?.runtimeType}");

    } catch (e) {
      setState(() {
        testResult = 'ERROR: $e';
        testPassed = false;
      });
      print("=== BAR CHART TEST ERROR ===");
      print("Error: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Bar Chart Implementation Test'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Test Result
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: testPassed ? Colors.green.shade100 : Colors.red.shade100,
                border: Border.all(
                  color: testPassed ? Colors.green : Colors.red,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                testResult,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: testPassed ? Colors.green.shade800 : Colors.red.shade800,
                ),
              ),
            ),
            
            SizedBox(height: 20),
            
            Expanded(
              child: Row(
                children: [
                  // JSON Output
                  if (jsonOutput.isNotEmpty) ...[
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Generated JSON:',
                            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                          ),
                          SizedBox(height: 8),
                          Expanded(
                            child: Container(
                              width: double.infinity,
                              padding: EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.grey.shade100,
                                border: Border.all(color: Colors.grey),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: SingleChildScrollView(
                                child: Text(
                                  jsonOutput,
                                  style: TextStyle(
                                    fontFamily: 'monospace',
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 16),
                  ],
                  
                  // Deserialized Widget
                  if (deserializedWidget != null)
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Deserialized Bar Chart:',
                            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                          ),
                          SizedBox(height: 8),
                          Expanded(
                            child: Container(
                              width: double.infinity,
                              padding: EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                border: Border.all(color: Colors.grey),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: deserializedWidget!,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
