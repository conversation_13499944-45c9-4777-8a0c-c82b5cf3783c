import 'package:flutter/material.dart';
import 'package:ui_controls_library/ui_controls_library.dart' as ui_controls;

void main() {
  runApp(const PieDonutChartTestApp());
}

class PieDonutChartTestApp extends StatelessWidget {
  const PieDonutChartTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Pie & Donut Chart Test',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const PieDonutChartTestScreen(),
    );
  }
}

class PieDonutChartTestScreen extends StatefulWidget {
  const PieDonutChartTestScreen({super.key});

  @override
  State<PieDonutChartTestScreen> createState() => _PieDonutChartTestScreenState();
}

class _PieDonutChartTestScreenState extends State<PieDonutChartTestScreen> {
  ui_controls.ChartSizeType selectedSize = ui_controls.ChartSizeType.medium;
  ui_controls.ChartType selectedChartType = ui_controls.ChartType.disc; // Start with pie chart

  // Test data matching the reference image
  Map<String, double> get testDataMap => {
    'Food': 20,
    'Rent': 15,
    'Transport': 10,
    'Savings': 12,
    'Others': 8,
    'Utilities': 10,
    'Insurance': 15,
    'Entertainment': 10,
  };

  // Colors matching the reference image
  List<Color> get testColorList => [
    const Color(0xFF1E88E5), // Blue for Food
    const Color(0xFF42A5F5), // Light Blue for Rent
    const Color(0xFF64B5F6), // Lighter Blue for Transport
    const Color(0xFF90CAF9), // Very Light Blue for Savings
    const Color(0xFF0D47A1), // Dark Blue for Others
    const Color(0xFF1565C0), // Medium Blue for Utilities
    const Color(0xFF1976D2), // Blue for Insurance
    const Color(0xFFBBDEFB), // Lightest Blue for Entertainment
  ];

  ui_controls.ChartSizeConfig get currentConfig {
    return ui_controls.ChartSizeConfig(
      size: selectedSize,
      headingFontSize: selectedSize == ui_controls.ChartSizeType.small ? 14 : 
                      selectedSize == ui_controls.ChartSizeType.medium ? 16 : 18,
      bodyFontSize: selectedSize == ui_controls.ChartSizeType.small ? 12 : 
                    selectedSize == ui_controls.ChartSizeType.medium ? 14 : 16,
      labelFontSize: selectedSize == ui_controls.ChartSizeType.small ? 10 : 
                     selectedSize == ui_controls.ChartSizeType.medium ? 12 : 14,
      chartRadius: selectedSize == ui_controls.ChartSizeType.small ? 150 : 
                   selectedSize == ui_controls.ChartSizeType.medium ? 180 : 220,
      propertyType: "Test Properties",
      borderThikness: selectedSize == ui_controls.ChartSizeType.small ? 0.5 : 
                      selectedSize == ui_controls.ChartSizeType.medium ? 1.0 : 2.0,
      borderRadius: selectedSize == ui_controls.ChartSizeType.small ? 6 : 
                    selectedSize == ui_controls.ChartSizeType.medium ? 16 : 24,
      elevation: selectedSize == ui_controls.ChartSizeType.small ? 1.0 : 
                 selectedSize == ui_controls.ChartSizeType.medium ? 1.5 : 2.0,
      chartType: selectedChartType,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Pie & Donut Chart Test'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Controls
            Row(
              children: [
                // Chart Type Toggle
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Chart Type',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 13,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 6),
                    Container(
                      width: 180,
                      height: 38,
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                        color: Colors.white,
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<ui_controls.ChartType>(
                          value: selectedChartType,
                          isExpanded: true,
                          icon: const Icon(Icons.arrow_drop_down),
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.black,
                            fontWeight: FontWeight.w500,
                          ),
                          dropdownColor: Colors.white,
                          focusColor: Colors.transparent,
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                selectedChartType = value;
                              });
                            }
                          },
                          items: [
                            DropdownMenuItem<ui_controls.ChartType>(
                              value: ui_controls.ChartType.disc,
                              child: const Text('Pie Chart'),
                            ),
                            DropdownMenuItem<ui_controls.ChartType>(
                              value: ui_controls.ChartType.ring,
                              child: const Text('Donut Chart'),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(width: 32),

                // Chart Size
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Chart Size',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 13,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 6),
                    Container(
                      width: 140,
                      height: 38,
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                        color: Colors.white,
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<ui_controls.ChartSizeType>(
                          value: selectedSize,
                          isExpanded: true,
                          icon: const Icon(Icons.arrow_drop_down),
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.black,
                            fontWeight: FontWeight.w500,
                          ),
                          dropdownColor: Colors.white,
                          focusColor: Colors.transparent,
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                selectedSize = value;
                              });
                            }
                          },
                          items: ui_controls.ChartSizeType.values.map((size) {
                            return DropdownMenuItem<ui_controls.ChartSizeType>(
                              value: size,
                              child: Text(size.name.toUpperCase()),
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Chart Display
            Expanded(
              child: ui_controls.RingPieChartUIBuilder(
                config: currentConfig,
                dataMap: testDataMap,
                colorList: testColorList,
                onPressed: () {
                  print('Chart button pressed');
                },
              ),
            ),

            // Test Information
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Test Information:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text('Chart Type: ${selectedChartType == ui_controls.ChartType.disc ? "Pie Chart" : "Donut Chart"}'),
                  Text('Chart Size: ${selectedSize.name.toUpperCase()}'),
                  Text('Data Points: ${testDataMap.length}'),
                  const Text('✓ Fixed pie chart rendering (full circle, no center hole)'),
                  const Text('✓ Improved donut chart with better center text positioning'),
                  const Text('✓ Enhanced serialization/deserialization support'),
                  const Text('✓ Consistent visual appearance across all sizes'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
