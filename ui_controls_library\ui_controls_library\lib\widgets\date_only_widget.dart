import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart' as intl;
import 'dart:async';
import 'dart:convert';
import 'package:ui_controls_library/utils/callback_interpreter.dart';

/// Format options for the date display
enum DateFormat {
  /// Standard format (e.g., "Jan 1, 2023")
  standard,

  /// Short format (e.g., "01/01/2023")
  short,

  /// Long format (e.g., "January 1, 2023")
  long,

  /// ISO format (e.g., "2023-01-01")
  iso,

  /// Custom format (specified by formatPattern)
  custom,
}

/// A comprehensive widget for displaying and selecting dates.
///
/// This widget provides extensive customization options for displaying
/// and selecting date values with various formats and styles.
class DateOnlyWidget extends StatefulWidget {
  /// The initial date value
  final DateTime? initialDate;

  /// Whether to allow selecting a date
  final bool allowDateSelection;

  /// Format of the date display
  final DateFormat format;

  /// Custom format pattern (used when format is DateFormat.custom)
  final String? formatPattern;

  /// Whether to show the weekday
  final bool showWeekday;

  /// Whether to show the year
  final bool showYear;

  /// Whether to show the month
  final bool showMonth;

  /// Whether to show the day
  final bool showDay;

  /// Whether to update the current date automatically
  final bool autoUpdate;

  /// Update interval in seconds for auto-update
  final int updateIntervalSeconds;

  /// Locale for formatting (e.g., 'en_US', 'fr_FR')
  final String locale;

  /// Text style for the date display
  final TextStyle? textStyle;

  /// Color of the text
  final Color textColor;

  /// Background color of the widget
  final Color backgroundColor;

  /// Size of the font
  final double fontSize;

  /// Weight of the font
  final FontWeight fontWeight;

  /// Font family
  final String? fontFamily;

  /// Alignment of the text
  final TextAlign textAlign;

  /// Whether to show a border
  final bool hasBorder;

  /// Radius of the border corners
  final double borderRadius;

  /// Color of the border
  final Color borderColor;

  /// Width of the border
  final double borderWidth;

  /// Whether to show a shadow
  final bool hasShadow;

  /// Elevation of the shadow
  final double elevation;

  /// Whether to use a compact layout
  final bool isCompact;

  /// Label text to display
  final String? label;

  /// Prefix text to display
  final String? prefix;

  /// Suffix text to display
  final String? suffix;

  /// Icon to display before the text
  final IconData? prefixIcon;

  /// Icon to display after the text
  final IconData? suffixIcon;

  /// Whether to use dark theme colors
  final bool isDarkTheme;

  /// Whether to show relative date (e.g., "2 days ago")
  final bool isRelative;

  /// Whether to show an icon
  final bool showIcon;

  /// Icon to display
  final IconData? icon;

  /// Color of the icon
  final Color? iconColor;

  /// Whether to animate the display
  final bool isAnimated;

  /// Custom text to display instead of formatted date
  final String? customText;

  /// Whether to convert text to uppercase
  final bool isUpperCase;

  /// Whether to convert text to lowercase
  final bool isLowerCase;

  /// Whether to capitalize the first letter of each word
  final bool isCapitalized;

  /// Whether to display text in italic
  final bool isItalic;

  /// Whether to display text in bold
  final bool isBold;

  /// Whether to underline the text
  final bool isUnderlined;

  /// Padding around the widget
  final double padding;

  /// Width of the widget
  final double? width;

  /// Height of the widget
  final double? height;

  /// Alignment of the main axis
  final MainAxisAlignment mainAxisAlignment;

  /// Alignment of the cross axis
  final CrossAxisAlignment crossAxisAlignment;

  /// Whether to show a clear button
  final bool showClearButton;

  /// Whether to show a today button (sets to current date)
  final bool showTodayButton;

  /// Whether to show a calendar icon for date selection
  final bool showCalendarIcon;

  /// Icon to use for calendar
  final IconData calendarIcon;

  /// Color of the calendar icon
  final Color? calendarIconColor;

  /// Whether the widget is enabled
  final bool enabled;

  /// Whether the widget is read-only
  final bool readOnly;

  /// Minimum selectable date
  final DateTime? minDate;

  /// Maximum selectable date
  final DateTime? maxDate;

  /// Callback when date changes
  final Function(DateTime)? onChanged;

  /// Callback when date is selected
  final Function(DateTime)? onSelected;

  // Advanced interaction properties
  /// Callback for mouse hover
  final void Function(bool)? onHover;

  /// Callback for keyboard focus
  final void Function(bool)? onFocus;

  /// Color when the widget is hovered
  final Color? hoverColor;

  /// Color when the widget is focused
  final Color? focusColor;

  /// Whether to enable haptic/audio feedback
  final bool enableFeedback;

  /// Callback for tap gesture
  final VoidCallback? onTap;

  /// Callback for double tap gesture
  final VoidCallback? onDoubleTap;

  /// Callback for long press gesture
  final VoidCallback? onLongPress;

  /// Focus node for controlling focus
  final FocusNode? focusNode;

  /// Whether the widget should autofocus
  final bool autofocus;

  // JSON callback properties
  /// Dynamic callback definitions from JSON
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use dynamic callbacks from JSON
  final bool useJsonCallbacks;

  /// State map for dynamic callbacks
  final Map<String, dynamic>? callbackState;

  /// Custom handlers for dynamic callbacks
  final Map<String, Function>? customCallbackHandlers;

  // Advanced date formatting options
  /// Custom date formatter function
  final String Function(DateTime)? customFormatter;

  /// Map of special date formats for specific dates
  final Map<String, String>? specialDateFormats;

  /// JSON configuration for the widget
  final Map<String, dynamic>? jsonConfig;

  /// Creates a date only widget.
  const DateOnlyWidget({
    super.key,
    this.initialDate,
    this.allowDateSelection = true,
    this.format = DateFormat.standard,
    this.formatPattern,
    this.showWeekday = false,
    this.showYear = true,
    this.showMonth = true,
    this.showDay = true,
    this.autoUpdate = false,
    this.updateIntervalSeconds = 1,
    this.locale = 'en_US',
    this.textStyle,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.fontFamily,
    this.textAlign = TextAlign.start,
    this.hasBorder = true,
    this.borderRadius = 4.0,
    this.borderColor = const Color(0xFFCCCCCC),
    this.borderWidth = 1.0,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.isCompact = false,
    this.label,
    this.prefix,
    this.suffix,
    this.prefixIcon,
    this.suffixIcon,
    this.isDarkTheme = false,
    this.isRelative = false,
    this.showIcon = false,
    this.icon,
    this.iconColor,
    this.isAnimated = false,
    this.customText,
    this.isUpperCase = false,
    this.isLowerCase = false,
    this.isCapitalized = false,
    this.isItalic = false,
    this.isBold = false,
    this.isUnderlined = false,
    this.padding = 16.0,
    this.width,
    this.height,
    this.mainAxisAlignment = MainAxisAlignment.center,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.showClearButton = false,
    this.showTodayButton = false,
    this.showCalendarIcon = true,
    this.calendarIcon = Icons.calendar_today,
    this.calendarIconColor,
    this.enabled = true,
    this.readOnly = false,
    this.minDate,
    this.maxDate,
    this.onChanged,
    this.onSelected,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.hoverColor = const Color(0xFF0058FF),
    this.focusColor = const Color(0xFF0058FF),
    this.enableFeedback = true,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.focusNode,
    this.autofocus = false,
    // JSON callback properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    // Advanced date formatting options
    this.customFormatter,
    this.specialDateFormats,
    this.jsonConfig,
  });

  /// Creates a DateOnlyWidget from a JSON map
  factory DateOnlyWidget.fromJson(Map<String, dynamic> json) {
    // Parse date format
    DateFormat format = DateFormat.standard;
    if (json['format'] != null) {
      switch (json['format'].toString().toLowerCase()) {
        case 'short':
          format = DateFormat.short;
          break;
        case 'long':
          format = DateFormat.long;
          break;
        case 'iso':
          format = DateFormat.iso;
          break;
        case 'custom':
          format = DateFormat.custom;
          break;
        case 'standard':
        default:
          format = DateFormat.standard;
          break;
      }
    }

    // Parse text alignment
    TextAlign textAlign = TextAlign.start;
    if (json['textAlign'] != null) {
      switch (json['textAlign'].toString().toLowerCase()) {
        case 'center':
          textAlign = TextAlign.center;
          break;
        case 'left':
        case 'start':
          textAlign = TextAlign.left;
          break;
        case 'right':
        case 'end':
          textAlign = TextAlign.right;
          break;
        case 'justify':
          textAlign = TextAlign.justify;
          break;
      }
    }

    // Parse font weight
    FontWeight fontWeight = FontWeight.normal;
    if (json['fontWeight'] != null) {
      if (json['fontWeight'] == 'bold' || json['fontWeight'] == true) {
        fontWeight = FontWeight.bold;
      } else if (json['fontWeight'] == 'light') {
        fontWeight = FontWeight.w300;
      }
    }

    // Parse colors
    Color? textColor;
    if (json['textColor'] != null) {
      textColor = _colorFromJson(json['textColor']);
    }

    Color? backgroundColor;
    if (json['backgroundColor'] != null) {
      backgroundColor = _colorFromJson(json['backgroundColor']);
    }

    Color? borderColor;
    if (json['borderColor'] != null) {
      borderColor = _colorFromJson(json['borderColor']);
    }

    Color? hoverColor;
    if (json['hoverColor'] != null) {
      hoverColor = _colorFromJson(json['hoverColor']);
    }

    Color? focusColor;
    if (json['focusColor'] != null) {
      focusColor = _colorFromJson(json['focusColor']);
    }

    return DateOnlyWidget(
      allowDateSelection: json['allowDateSelection'] as bool? ?? true,
      format: format,
      formatPattern: json['formatPattern'] as String?,
      showWeekday: json['showWeekday'] as bool? ?? false,
      showYear: json['showYear'] as bool? ?? true,
      showMonth: json['showMonth'] as bool? ?? true,
      showDay: json['showDay'] as bool? ?? true,
      autoUpdate: json['autoUpdate'] as bool? ?? false,
      updateIntervalSeconds: json['updateIntervalSeconds'] as int? ?? 1,
      locale: json['locale'] as String? ?? 'en_US',
      textColor: textColor ?? Colors.black,
      backgroundColor: backgroundColor ?? Colors.white,
      fontSize: (json['fontSize'] as num?)?.toDouble() ?? 16.0,
      fontWeight: fontWeight,
      fontFamily: json['fontFamily'] as String?,
      textAlign: textAlign,
      hasBorder: json['hasBorder'] as bool? ?? true,
      borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 4.0,
      borderColor: borderColor ?? const Color(0xFFCCCCCC),
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 1.0,
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation: (json['elevation'] as num?)?.toDouble() ?? 2.0,
      isCompact: json['isCompact'] as bool? ?? false,
      label: json['label'] as String?,
      prefix: json['prefix'] as String?,
      suffix: json['suffix'] as String?,
      isDarkTheme: json['isDarkTheme'] as bool? ?? false,
      enabled: json['enabled'] as bool? ?? true,
      readOnly: json['readOnly'] as bool? ?? false,
      hoverColor: hoverColor,
      focusColor: focusColor,
      useJsonCallbacks: json['useJsonCallbacks'] as bool? ?? false,
      jsonConfig: json,
    );
  }

  /// Converts a JSON color value to a Flutter Color
  static Color? _colorFromJson(dynamic colorValue) {
    if (colorValue == null) return null;

    if (colorValue is String) {
      // Handle hex strings like "#FF0000"
      if (colorValue.startsWith('#')) {
        String hexColor = colorValue.substring(1);

        // Handle shorthand hex like #RGB
        if (hexColor.length == 3) {
          hexColor = hexColor.split('').map((c) => '$c$c').join('');
        }

        // Add alpha channel if missing
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }

        // Parse the hex value
        try {
          return Color(int.parse('0x$hexColor'));
        } catch (e) {
          return null;
        }
      }

      // Handle named colors
      switch (colorValue.toLowerCase()) {
        case 'red':
          return Colors.red;
        case 'blue':
          return Colors.blue;
        case 'green':
          return Colors.green;
        case 'yellow':
          return Colors.yellow;
        case 'orange':
          return Colors.orange;
        case 'purple':
          return Colors.purple;
        case 'pink':
          return Colors.pink;
        case 'brown':
          return Colors.brown;
        case 'grey':
        case 'gray':
          return Colors.grey;
        case 'black':
          return Colors.black;
        case 'white':
          return Colors.white;
        default:
          return null;
      }
    } else if (colorValue is int) {
      return Color(colorValue);
    }

    return null;
  }

  @override
  State<DateOnlyWidget> createState() => _DateOnlyWidgetState();
}

class _DateOnlyWidgetState extends State<DateOnlyWidget> {
  late DateTime _selectedDate;
  String _formattedDate = '';
  bool _hasUserSelectedDate = false;

  // State for hover and focus
  bool _isHovered = false;
  bool _hasFocus = false;

  // State for dynamic callbacks
  late Map<String, dynamic> _callbackState;

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.initialDate ?? DateTime.now();
    _hasUserSelectedDate = widget.initialDate != null;
    _updateFormattedDate();

    // Initialize callback state
    _callbackState = widget.callbackState?.cast<String, dynamic>() ?? {};
  }

  void _updateFormattedDate() {
    // Show placeholder if user hasn't selected a date yet
    if (!_hasUserSelectedDate) {
      _formattedDate = 'DD/MM/YYYY';
      return;
    }

    if (widget.customFormatter != null) {
      _formattedDate = widget.customFormatter!(_selectedDate);
      return;
    }

    // Handle relative dates
    if (widget.isRelative) {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final selectedDay = DateTime(
        _selectedDate.year,
        _selectedDate.month,
        _selectedDate.day,
      );

      if (selectedDay == today) {
        _formattedDate = 'Today';
        return;
      } else if (selectedDay == today.subtract(const Duration(days: 1))) {
        _formattedDate = 'Yesterday';
        return;
      } else if (selectedDay == today.add(const Duration(days: 1))) {
        _formattedDate = 'Tomorrow';
        return;
      }
    }

    // Create date formatter based on locale
    late intl.DateFormat formatter;

    switch (widget.format) {
      case DateFormat.short:
        formatter = intl.DateFormat.yMd(widget.locale);
        break;
      case DateFormat.long:
        formatter = intl.DateFormat.yMMMMd(widget.locale);
        break;
      case DateFormat.iso:
        formatter = intl.DateFormat('yyyy-MM-dd');
        break;
      case DateFormat.custom:
        if (widget.formatPattern != null) {
          formatter = intl.DateFormat(widget.formatPattern!, widget.locale);
        } else {
          formatter = intl.DateFormat.yMd(widget.locale);
        }
        break;
      case DateFormat.standard:
      default:
        formatter = intl.DateFormat.yMMMd(widget.locale);
        break;
    }

    // Format the date
    String formattedDate = formatter.format(_selectedDate);

    // Handle component visibility flags
    if (!widget.showYear || !widget.showMonth || !widget.showDay) {
      // Create a custom format based on visibility flags
      List<String> components = [];

      if (widget.showWeekday) {
        components.add(intl.DateFormat.E(widget.locale).format(_selectedDate));
      }

      if (widget.showMonth) {
        if (widget.format == DateFormat.long) {
          components.add(
            intl.DateFormat.MMMM(widget.locale).format(_selectedDate),
          );
        } else {
          components.add(
            intl.DateFormat.MMM(widget.locale).format(_selectedDate),
          );
        }
      }

      if (widget.showDay) {
        components.add(intl.DateFormat.d(widget.locale).format(_selectedDate));
      }

      if (widget.showYear) {
        components.add(intl.DateFormat.y(widget.locale).format(_selectedDate));
      }

      if (components.isNotEmpty) {
        formattedDate = components.join(' ');
      }
    } else if (widget.showWeekday) {
      // Add weekday to the formatted date
      String weekday = intl.DateFormat.E(widget.locale).format(_selectedDate);
      formattedDate = '$weekday, $formattedDate';
    }

    _formattedDate = formattedDate;
  }

  /// Handles hover state changes
  void _onHoverChange(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }

      // Execute dynamic callback if enabled
      if (widget.useJsonCallbacks &&
          widget.jsonCallbacks != null &&
          widget.jsonCallbacks!.containsKey('onHover')) {
        final callback = widget.jsonCallbacks!['onHover'];
        CallbackInterpreter.executeCallback(
          callback,
          context,
          value: isHovered,
          state: _callbackState,
          customHandlers: widget.customCallbackHandlers,
        );
      }
    });
  }

  /// Handles focus state changes
  void _onFocusChange(bool hasFocus) {
    setState(() {
      _hasFocus = hasFocus;
      if (widget.onFocus != null) {
        widget.onFocus!(hasFocus);
      }

      // Execute dynamic callback if enabled
      if (widget.useJsonCallbacks &&
          widget.jsonCallbacks != null &&
          widget.jsonCallbacks!.containsKey('onFocus')) {
        final callback = widget.jsonCallbacks!['onFocus'];
        CallbackInterpreter.executeCallback(
          callback,
          context,
          value: hasFocus,
          state: _callbackState,
          customHandlers: widget.customCallbackHandlers,
        );
      }
    });
  }

  Future<void> _selectDate() async {
    if (!widget.enabled || widget.readOnly || !widget.allowDateSelection)
      return;

    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: widget.minDate ?? DateTime(1900),
      lastDate: widget.maxDate ?? DateTime(2100),
      builder: (context, child) {
        return Theme(
          data: ThemeData(
            // useMaterial3: false,
            colorScheme: ColorScheme.light(
              primary: const Color(0xFF0058FF),
              onBackground: const Color(0xFFCCCCCC),
            ),
          ),
          child: child ?? SizedBox(),
        );
      },
    );

    if (pickedDate != null && pickedDate != _selectedDate) {
      setState(() {
        _selectedDate = pickedDate;
        _hasUserSelectedDate = true;
        _updateFormattedDate();
      });

      if (widget.onChanged != null) {
        widget.onChanged!(_selectedDate);
      }

      if (widget.onSelected != null) {
        widget.onSelected!(_selectedDate);
      }

      if (widget.onTap != null) {
        widget.onTap!();
      }

      // Execute dynamic callbacks
      if (widget.useJsonCallbacks && widget.jsonCallbacks != null) {
        // Schedule callbacks to run after the async gap
        Future.microtask(() {
          if (!mounted) return;

          // Execute onChanged callback
          if (widget.jsonCallbacks!.containsKey('onChanged')) {
            final callback = widget.jsonCallbacks!['onChanged'];
            CallbackInterpreter.executeCallback(
              callback,
              context,
              value: _selectedDate.toIso8601String(),
              state: _callbackState,
              customHandlers: widget.customCallbackHandlers,
            );
          }

          // Execute onSelected callback
          if (widget.jsonCallbacks!.containsKey('onSelected')) {
            final callback = widget.jsonCallbacks!['onSelected'];
            CallbackInterpreter.executeCallback(
              callback,
              context,
              value: _selectedDate.toIso8601String(),
              state: _callbackState,
              customHandlers: widget.customCallbackHandlers,
            );
          }
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Apply dark theme if specified
    final effectiveTextColor =
        widget.isDarkTheme ? Colors.white : widget.textColor;
    final effectiveBackgroundColor =
        widget.isDarkTheme ? Colors.grey.shade800 : widget.backgroundColor;

    // Create text style
    TextStyle textStyle =
        widget.textStyle ??
        TextStyle(
          color: effectiveTextColor,
          fontSize: widget.fontSize,
          fontWeight: widget.fontWeight,
          fontFamily: widget.fontFamily,
        );
    // Get responsive font size based on screen width
    double getResponsiveTitleFontSize(double screenWidth) {
      if (screenWidth > 1920) {
        return 18.0; // Extra Large
      } else if (screenWidth >= 1440) {
        return 16.0; // Large
      } else if (screenWidth >= 1280) {
        return 14.0; // Medium
      } else {
        return 14.0; // Default for very small screens
      }
    }

    final double screenWidth = MediaQuery.of(context).size.width;
    final double responsiveTitleFontSize = getResponsiveTitleFontSize(
      screenWidth,
    );
    // Get responsive icon size based on screen width
    double getResponsiveIconSize(double screenWidth) {
      if (screenWidth > 1920) {
        return 22.0; // Extra Large
      } else if (screenWidth >= 1440) {
        return 20.0; // Large
      } else if (screenWidth >= 1280) {
        return 18.0; // Medium
      } else if (screenWidth >= 768) {
        return 16.0; // Small
      } else {
        return 12.0; // Extra Small (fallback for very small screens)
      }
    }

    final double responsiveIconSize = getResponsiveIconSize(screenWidth);
    // Create the main content widget
    Widget content = MouseRegion(
      onEnter: (_) => _onHoverChange(true),
      onExit: (_) => _onHoverChange(false),
      cursor: SystemMouseCursors.click,
      child: Focus(
        focusNode: widget.focusNode,
        autofocus: widget.autofocus,
        onFocusChange: _onFocusChange,
        child: GestureDetector(
          onTap: () => _selectDate(),
          onDoubleTap: widget.onDoubleTap,
          onLongPress: widget.onLongPress,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            height: _getResponsiveHeight(context),
            padding: _getResponsivePadding(context),
            decoration: BoxDecoration(
              color: effectiveBackgroundColor,
              borderRadius: BorderRadius.circular(widget.borderRadius),
              border:
                  widget.hasBorder
                      ? Border.all(
                        color:
                            _isHovered
                                ? (widget.hoverColor ?? const Color(0xFF0058FF))
                                : _hasFocus
                                ? (widget.focusColor ?? const Color(0xFF0058FF))
                                : widget.borderColor,
                        width: widget.borderWidth,
                      )
                      : null,
              boxShadow:
                  widget.hasShadow
                      ? [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: widget.elevation,
                          offset: Offset(0, widget.elevation / 2),
                        ),
                      ]
                      : null,
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                if (widget.prefix != null) ...[
                  Text(
                    widget.prefix!,
                    style: TextStyle(
                      color: effectiveTextColor.withOpacity(0.7),
                      fontSize: widget.fontSize,
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
                Expanded(
                  child: Text(
                    _formattedDate,
                    style: textStyle.copyWith(
                      color: effectiveTextColor.withOpacity(0.6),
                      fontSize: responsiveTitleFontSize,
                    ),
                    textAlign: TextAlign.left,
                  ),
                ),
                if (widget.suffix != null) ...[
                  const SizedBox(width: 8),
                  Text(
                    widget.suffix!,
                    style: TextStyle(
                      color: effectiveTextColor.withOpacity(0.7),
                      fontSize: widget.fontSize,
                    ),
                  ),
                ],
                if (widget.showCalendarIcon) ...[
                  const SizedBox(width: 8),
                  SvgPicture.asset(
                    _isHovered
                        ? 'assets/images/icon-date-hover.svg'
                        : 'assets/images/icon-date.svg',
                    package: 'ui_controls_library',
                    width: responsiveIconSize,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );

    // Build the final widget with label to match the image
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [content],
    );
  }
}

EdgeInsets _getResponsivePadding(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth >= 1440) {
    return const EdgeInsets.symmetric(
      horizontal: 16.0,
      vertical: 4.0,
    ); // Extra Large
  } else if (screenWidth >= 1280) {
    return const EdgeInsets.symmetric(
      horizontal: 12.0,
      vertical: 3.0,
    ); // Large// Large
  } else if (screenWidth >= 768) {
    return const EdgeInsets.symmetric(
      horizontal: 8.0,
      vertical: 2.0,
    ); // Medium// Medium
  } else {
    return const EdgeInsets.symmetric(
      horizontal: 6.0,
      vertical: 1.0,
    ); // Default for very small screens
  }
}

double _getResponsiveHeight(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 56.0; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 48.0; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 40.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 32.0; // Small (768-1024px)
  } else {
    return 32.0; // Default for very small screens
  }
}
