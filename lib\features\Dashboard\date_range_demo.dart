import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:ui_controls_library/widgets/ui_builder/flexible_widget_serializer.dart';
import 'package:ui_controls_library/widgets/date_range_widget.dart';

class DateRangeDemoScreen extends StatefulWidget {
  const DateRangeDemoScreen({super.key});

  @override
  State<DateRangeDemoScreen> createState() => _DateRangeDemoScreenState();
}

class _DateRangeDemoScreenState extends State<DateRangeDemoScreen> {
  late Widget screenWidgetTree;
  String jsonOutput = '';
  Widget? deserializedWidget;

  @override
  void initState() {
    super.initState();
    _rebuildWidgetTree();
  }

  void _rebuildWidgetTree() {
    screenWidgetTree = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "DateRange Widget Demo", 
          style: TextStyle(fontSize: 33, fontWeight: FontWeight.bold)
        ),
        const SizedBox(height: 32),
        _buildDateRangeSection(),
      ],
    );
  }

  Widget _buildDateRangeSection() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
        boxShadow: const [BoxShadow(color: Colors.black12, blurRadius: 4)],
      ),
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            "Date Range Selection", 
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600)
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildDateRangeWidget(),
              ),
              const SizedBox(width: 20),
              Expanded(
                child: _buildDateRangeProperties(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDateRangeWidget() {
    return DateRangeWidget(
      initialStartDate: DateTime.now().subtract(const Duration(days: 7)),
      initialEndDate: DateTime.now(),
      allowDateSelection: true,
      format: DateRangeFormat.standard,
      showWeekday: false,
      showYear: true,
      showMonth: true,
      showDay: true,
      textColor: Colors.black,
      backgroundColor: Colors.white,
      fontSize: 16.0,
      fontWeight: FontWeight.normal,
      hasBorder: true,
      borderRadius: 8.0,
      borderColor: const Color(0xFFCCCCCC),
      borderWidth: 1.0,
      hasShadow: false,
      elevation: 2.0,
      showCalendarIcon: true,
      enabled: true,
      readOnly: false,
      onChanged: (startDate, endDate) {
        print("Date range changed: $startDate - $endDate");
      },
      onSelected: (startDate, endDate) {
        print("Date range selected: $startDate - $endDate");
      },
    );
  }

  Widget _buildDateRangeProperties() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "Properties",
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)
        ),
        const SizedBox(height: 8),
        const Text("Format: Standard"),
        const Text("Show Weekday: false"),
        const Text("Show Year: true"),
        const Text("Border Radius: 8.0"),
        const Text("Font Size: 16.0"),
        const Text("Enabled: true"),
        const Text("Read Only: false"),
      ],
    );
  }

  Future<void> generateJson() async {
    try {
      print("=== Starting DateRange JSON Generation ===");
      
      // Generate JSON from current widget tree
      final generated = FlexibleWidgetSerializer.serialize(screenWidgetTree);
      final jsonString = const JsonEncoder.withIndent('  ').convert(generated);
      
      setState(() {
        jsonOutput = jsonString;
      });
      
      print("=== DateRange JSON Generation Complete ===");
      print("JSON Length: ${jsonString.length} characters");
    } catch (e) {
      print("Error in JSON generation: $e");
      setState(() {
        jsonOutput = 'Error: $e';
      });
    }
  }

  Future<void> generateBuildUI() async {
    try {
      print("=== Starting DateRange JSON to Widget Conversion ===");
      
      // Generate JSON from current widget tree
      final generated = FlexibleWidgetSerializer.serialize(screenWidgetTree);
      final jsonString = const JsonEncoder.withIndent('  ').convert(generated);
      
      print("=== DateRange Serialized Widget JSON ===\n$jsonString");
      
      // Deserialize JSON back to widget
      final rebuiltWidget = FlexibleWidgetSerializer.deserialize(generated);
      
      setState(() {
        deserializedWidget = rebuiltWidget;
      });
      
      print("=== DateRange JSON to Widget Conversion Complete ===");
    } catch (e) {
      print("Error in JSON generation/deserialization: $e");
      setState(() {
        jsonOutput = 'Error: $e';
        deserializedWidget = null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Generate JSON Button
          Row(
            children: [
              ElevatedButton.icon(
                onPressed: generateJson,
                icon: const Icon(Icons.code),
                label: const Text('Generate JSON'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  textStyle: const TextStyle(fontSize: 16),
                ),
              ),
              const SizedBox(width: 16),
              ElevatedButton.icon(
                onPressed: generateBuildUI,
                icon: const Icon(Icons.build),
                label: const Text('Generate Build UI'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  textStyle: const TextStyle(fontSize: 16),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          
          // Original Widget
          Container(
            width: double.infinity,
            margin: const EdgeInsets.only(bottom: 16),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.blue.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Original Widget',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                const SizedBox(height: 8),
                screenWidgetTree,
              ],
            ),
          ),
          
          // Side by side layout for JSON and Deserialized Widget
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // JSON Output
              if (jsonOutput.isNotEmpty) ...[
                Expanded(
                  child: Container(
                    height: 400,
                    margin: const EdgeInsets.all(8),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.blue.shade200),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'JSON Output',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                        ),
                        const SizedBox(height: 8),
                        Expanded(
                          child: SingleChildScrollView(
                            child: SelectableText(
                              jsonOutput,
                              style: const TextStyle(fontSize: 12, fontFamily: 'Courier'),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
              
              // Deserialized Widget
              if (deserializedWidget != null)
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.all(8),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.green.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Deserialized Widget',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                        ),
                        const SizedBox(height: 8),
                        deserializedWidget!,
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }
}
